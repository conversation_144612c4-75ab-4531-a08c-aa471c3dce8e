<!DOCTYPE html>
<html>

<head>
    <title>任务分配</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport"
        content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <!-- 基础的 css js 资源 -->
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0">
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0">
    <link rel="stylesheet" href="/cc-quality/static/css/qualityControl.css?v=1.0.1">
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
    <script src="/cc-quality/static/lib/wavesurfer/wavesurfer.js"></script>
    <script src="/cc-quality/static/lib/wavesurfer/wavesurfer.timeline.min.js"></script>
    <script type="text/javascript" src="/cc-quality/static/js/my_i18n.js?v=2021110901"></script>
    <script type="text/javascript" src="/cc-base/static/js/i18n.js?v=20140426"></script>

    <style>
        .yq-table-page {
            background-color: #fff;
        }

        .title-box {
            padding: 16px;
            border-bottom: 1px solid #E8E8E8;
            display: flex;
            justify-content: space-between;
        }

        .search-box {
            padding: 10px 24px;
        }

        .el-form.el-form--inline .el-input__inner {
            height: auto !important;
            line-height: auto !important;
            background: #f2f4f7;
        }

        .table-box {
            padding: 24px;
            padding-top: 0;
        }

        .inspector-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            gap: 10px;
        }

        .is-hide .el-checkbox {
            display: none;
        }

        .reset-form-btn {
            position: absolute;
            left: 24px;
            color: #0555CE;
            cursor: pointer;
        }
    </style>
</head>

<body class="yq-page-full vue-box">
    <div id="QcMediaAllocation" class="flex yq-table-page" element-loading-text="加载中..." v-cloak>
        <div class="flex yq-card">
            <input type="hidden" name="expSortName" id="expSortName" placeholder="layui表头的排序字段，仅导出使用"
                style="height: 30px;" />
            <input type="hidden" name="expSortType" id="expSortType" placeholder="layui表头的排序字段，仅导出使用"
                style="height: 30px;" />

            <div class="title-box">
                <div class="title">{{getI18nValue('自定义渠道-任务分配')}}</div>
                <div>
                    <el-button size="small" type="primary" plain
                        @click="showStatQuery">{{getI18nValue('统计指标')}}</el-button>
                    <el-button size="small" type="primary" plain
                        @click="toLink('BZ')">{{getI18nValue('标准渠道')}}</el-button>
                    <el-button size="small" type="primary" plain
                        @click="showConditionDialog">{{getI18nValue('条件分配')}}</el-button>
                    <el-button size="small" type="primary"
                        @click="changeInspectorAll">{{getI18nValue('批量分配')}}</el-button>
                </div>
            </div>
            <div class="card-content">
                <senior-search :show.sync="show">
                    <el-form :inline="false" :model="formData" ref="form" class="search-form" size="small"
                        label-width="120px">
                        <!-- 关键字--keyword -->
                        <el-form-item :label="getI18nValue('关键字')" prop="keyword">
                            <el-input v-model="formData.keyword" :placeholder="getI18nValue('关键字')"></el-input>
                        </el-form-item>
                        <!-- 渠道名称--channelName -->
                        <el-form-item :label="getI18nValue('日期')" prop="date" label-width="50px">
                            <el-date-picker v-model="formData.date" type="daterange" align="right" unlink-panels
                                range-separator="~" :start-placeholder="getI18nValue('开始日期')"
                                :end-placeholder="getI18nValue('结束日期')" style="width:100%" value-format="yyyy-MM-dd"
                                clearable>
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item :label="getI18nValue('模板')" prop="templateId">
                            <el-select v-model="formData.templateId" :placeholder="getI18nValue('模板')" clearable
                                @change="getTaskList">
                                <el-option v-for="(key,value) of TEMPLATE_LIST" :label="key" :value="value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="getI18nValue('任务名称')" prop="taskId" v-show="show">
                            <el-select v-model="formData.taskId" :placeholder="getI18nValue('任务名称')" clearable
                                filterable @change="getZnRuleItems">
                                <el-option v-for="(key,value) of TASKBYCHANNEL" :label="key" :value="value"></el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item :label="getI18nValue('智能质检状态')" prop="znQcStatus" v-show="show">
                            <el-select v-model="formData.znQcStatus" :placeholder="getI18nValue('请选择')" clearable>
                                <el-option v-for="(key,value) of znStateOptions" :label="key"
                                    :value="value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="getI18nValue('是否聚合')" prop="isAggregate" v-show="show">
                            <el-select v-model="formData.isAggregate" :placeholder="getI18nValue('请选择')" clearable>
                                <el-option :label="getI18nValue('聚合')" value="1"></el-option>
                                <el-option :label="getI18nValue('非聚合')" value="0"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="getI18nValue('质检规则')" prop="znItemIds" v-show="show">
                            <el-cascader :options="znResultItemList" style="width: 100%;" v-model="formData.znItemIds"
                                :props="{ multiple: true, checkStrictly: true, value: 'itemId', label: 'itemName' }"
                                clearable></el-cascader>
                        </el-form-item>
                        <el-form-item v-for="(item,index) in sels" :key="'sel'+index" :label="getI18nValue(item.label)"
                            :prop="item.id" :label-width="(item.searchWidth || 100)+'px'" v-show="show">
                            <template v-if="item.showType=='1'">
                                <el-input v-model="formData.form[item.id]" :placeholder="getI18nValue(item.label)"
                                    clearable></el-input>
                            </template>
                            <template v-else-if="item.showType=='2'">
                                <el-select v-model="formData.form[item.id]" :placeholder="getI18nValue(item.label)"
                                    clearable style="width: 100%;">
                                    <el-option v-for="(tem,dex) in item.arr" :label="tem.value"
                                        :value="tem.key"></el-option>
                                </el-select>
                            </template>
                            <template v-else-if="item.showType=='3'">
                                <el-date-picker v-model="formData.form[item.id]"
                                    :type="item.dateType=='date'? 'date' : 'datetime'" style="width:100%" align="right"
                                    unlink-panels range-separator="~" :start-placeholder="getI18nValue('开始时间')"
                                    :end-placeholder="getI18nValue('结束时间')" :placeholder="getI18nValue(item.label)"
                                    :value-format="item.format" clearable>
                                </el-date-picker>
                            </template>
                            <template v-else-if="item.showType=='4'">
                                <el-cascader v-model="formData.form[item.id]" :options="item.tree" :props="bs_prop"
                                    style="width:100%" :placeholder="getI18nValue(item.label)" clearable></el-cascader>
                            </template>
                        </el-form-item>

                        <el-form-item class="btns" label-width="0px">
                            <el-button type="primary" size="small" icon="el-icon-search"
                                @click="doSearch">{{getI18nValue('查询')}}</el-button>
                            <el-button type="primary" plain size="small" icon="el-icon-refresh"
                                @click="reset">{{getI18nValue('重置')}}</el-button>
                            <el-button type="primary" plain size="small" icon="el-icon-arrow-down"
                                @click="show=!show">{{getI18nValue('高级查询')}}</el-button>
                        </el-form-item>

                    </el-form>
                </senior-search>
                <div class="yq-table">
                    <template v-if="formData.templateId">
                        <el-table :data="tableData" @selection-change="handleSelectionChanges" stripe fit ref="table"
                            style="width: 100%" height="100%" v-loading="pageNav.loading" row-key="OBJ_ID"
                            :row-class-name="setRowClass" :tree-props="{children: 'children'}">
                            <el-table-column type="selection" width="60px" fixed></el-table-column>
                            <el-table-column type="index" :label="getI18nValue('序号')" width="50" fixed>
                            </el-table-column>
                            <el-table-column :label="getI18nValue('任务名称')" prop="TASK_NAME" width="180" fixed></el-table-column>

                            <!-- 自定义配置 -->
                            <template v-for="(item,index) in cols">
                                <el-table-column :prop="item.key" :label="getI18nValue(item.title)" min-width="180"
                                    :key="index" show-overflow-tooltip v-if="item.key=='THIRD_BUSI_TYPE'">
                                    <template
                                        slot-scope="scope">{{THIRD_PARTY_BUSI_TYPE[scope.row[item.key]]}}</template>
                                </el-table-column>
                                <el-table-column :prop="item.key" :label="getI18nValue(item.title)" min-width="180"
                                    :key="index" show-overflow-tooltip v-else-if="item.key=='THIRD_BUSI_DIRECTION'">
                                    <template
                                        slot-scope="scope">{{THIRD_PARTY_BUSI_DIRECTION[scope.row[item.key]]}}</template>
                                </el-table-column>
                                <el-table-column :prop="item.key" :label="getI18nValue(item.title)" min-width="180"
                                    :key="index" show-overflow-tooltip v-else-if="item.key=='THIRD_SOURCE'">
                                    <template slot-scope="scope">{{THIRD_PARTY_SOURCE[scope.row[item.key]]}}</template>
                                </el-table-column>
                                <el-table-column :prop="item.key" :label="getI18nValue(item.title)" min-width="180" :sortable="item.dateType == 'time'"
                                    :key="index" show-overflow-tooltip v-else>
                                </el-table-column>
                            </template>

                            <el-table-column prop="opt" :label="getI18nValue('操作')" width="200" fixed="right">
                                <template slot-scope="scope">
                                    <el-link type="primary" size="small"
                                        @click="changeInspector(scope.row.OBJ_ID,scope.row.EXAM_GROUP_ID, scope.row)">
                                        {{getI18nValue('分配质检员')}}
                                    </el-link>
                                    <el-link v-if="scope.row.ZN_STATE == '4'" type="primary" size="small" @click="goDetail(scope.row)" style="margin-left: 10px">
                                        {{getI18nValue('查看详情')}}
                                    </el-link>
                                </template>
                            </el-table-column>

                        </el-table>
                        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" background
                            :current-page="pageNav.page" :page-sizes="[15, 30, 50, 100]" :page-size="pageNav.size"
                            layout="total, sizes, prev, pager, next, jumper" :total="pageNav.total">
                        </el-pagination>
                    </template>
                    <template v-else>
                        <el-empty description="请先选择任务！"></el-empty>
                    </template>
                </div>
            </div>
            <div>
                <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" @close="reset2">
                    <div>
                        <div class="search-box">
                            <el-form :inline="true" :model="formData2" ref="form2" inline>
                                <el-form-item :label="getI18nValue('角色')" prop="roleId">
                                    <el-select v-model="formData2.roleId" :placeholder="getI18nValue('角色')" clearable
                                        filterable>
                                        <el-option v-for="(key,value) of roleDict" :label="key"
                                            :value="value"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item :label="getI18nValue('坐席账号')" prop="agentPhone">
                                    <el-input v-model="formData2.agentPhone" :placeholder="getI18nValue('')"></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" size="small" icon="el-icon-search"
                                        @click="doSearch2">{{getI18nValue('查询')}}</el-button>
                                </el-form-item>
                            </el-form>
                        </div>
                        <div class="table-box yq-table">
                            <el-table ref="tb" @selection-change="handleSelectionChange" stripe :data="tableData2" fit
                                v-loading="pageNav2.loading">
                                <el-table-column label="" type="selection" width="60px" fixed></el-table-column>
                                <el-table-column type="index" :label="getI18nValue('序号')" width="50" fixed>
                                </el-table-column>
                                <el-table-column prop="AGENT_NAME" :label="getI18nValue('姓名')" min-width="120"
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="USER_ACCT" :label="getI18nValue('坐席账号')" min-width="120"
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="ADD_TIME" :label="getI18nValue('添加时间')" min-width="120"
                                    show-overflow-tooltip>
                                </el-table-column>
                            </el-table>
                            <el-pagination @size-change="handleSizeChange2" @current-change="handleCurrentChange2"
                                background :current-page="pageNav2.page" :page-sizes="[15, 30, 50, 100]"
                                :page-size="pageNav2.size" layout="total, sizes, prev, pager, next, jumper"
                                :total="pageNav2.total">
                            </el-pagination>
                        </div>
                    </div>
                    <div slot="footer" class="dialog-footer">
                        <el-button @click="dialogFormVisible = false">取 消</el-button>
                        <el-button type="primary" @click="save">确 定</el-button>
                    </div>
                </el-dialog>
            </div>
            <!-- 修改条件分配弹窗 -->
            <el-dialog
                :title="getI18nValue('条件分配') + ' (' + getI18nValue('已选择') + selectedCount + getI18nValue('条记录') + ')'"
                :visible.sync="conditionDialogVisible" width="600px">
                <el-form :model="conditionForm" ref="conditionForm" label-width="120px">
                    <el-form-item :label="getI18nValue('分配类型')" prop="type">
                        <el-radio-group v-model="conditionForm.type">
                            <el-radio label="1">{{getI18nValue('按数量分配')}}</el-radio>
                            <el-radio label="2">{{getI18nValue('按比例分配')}}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('质检员分配')" required>
                        <div style="max-height: 200px;overflow: auto;">
                            <div v-for="(item, index) in conditionForm.inspectorArray" :key="index"
                                class="inspector-item">
                                <el-select filterable v-model="item.inspectorAcc" placeholder="选择质检员"
                                    style="width: 200px;">
                                    <el-option v-for="inspector in inspectorList" :key="inspector.USER_ACCT"
                                        :label="inspector.AGENT_NAME" :value="inspector.USER_ACCT">
                                    </el-option>
                                </el-select>
                                <template v-if="conditionForm.type === '1'">
                                    <el-input-number v-model="item.nums" controls-position="right" :min="1"
                                        :max="selectedCount" placeholder="分配数量">
                                    </el-input-number>
                                </template>
                                <template v-if="conditionForm.type === '2'">
                                    <el-input-number v-model="item.rate" controls-position="right" :min="0" :max="100"
                                        placeholder="分配比例">
                                    </el-input-number>
                                    <span>%</span>
                                </template>
                                <el-button type="text" @click="removeInspector(index)"
                                    icon="el-icon-delete"></el-button>
                            </div>
                            <el-button type="text" icon="el-icon-plus"
                                @click="addInspector">{{getI18nValue('添加质检员')}}</el-button>
                        </div>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button @click="conditionDialogVisible = false">{{getI18nValue('取消')}}</el-button>
                    <el-button type="primary" @click="submitConditionDistribution">{{getI18nValue('确定')}}</el-button>
                </div>
            </el-dialog>
            <!-- 统计指标 -->
            <el-drawer title="统计指标" :visible.sync="show_query_drawer" size="50%" :wrapper-closable="false">
                <div class="drawer-content">
                    <zdy-query ref="zdy_query"></zdy-query>
                </div>
                <div class="drawer-footer">
                    <div class="reset-form-btn" @click="resetStatQuery">
                        <i class="el-icon-refresh"></i> {{getI18nValue('重置')}}
                    </div>
                    <el-button type="primary" plain
                        @click="()=>{$refs.zdy_query.resetFields();show_query_drawer=false}">{{getI18nValue('关闭')}}</el-button>
                    <el-button type="primary" @click="queryByStat">{{getI18nValue('查询')}}</el-button>
                </div>
            </el-drawer>
        </div>
    </div>

    <script>
        // 每个页面的容器id和实例变量要不一样
        var orderFramePage = new Vue({
            components: {
                'zdy-query': httpVueLoader('/cc-quality/pages/qualityControl/components/zdy_query.vue')
            },
            el: '#QcMediaAllocation',
            data: function () {
                return {
                    show: false,
                    dialogTitle: "",
                    fileList: [],
                    dialogFormVisible: false,
                    formLabelWidth: "120px",
                    showDetailValue: true,
                    formData: {
                        date: [],
                        keyword: '',
                        beginStartDate: '',
                        endStartDate: '',
                        channelType: "9",
                        znQcStatus: '',
                        qcStatus: "1",
                        templateId: '',
                        taskId: "",
                        isAggregate: '',
                        form: {
                        },

                        znItemIds: []
                    },
                    bs_prop: {
                        value: 'id',
                        label: 'name',
                        children: 'children'
                    },
                    znStateOptions: {
                        "0": "未质检",
                        "1": "质检中",
                        "2": "已质检",
                        "3": "质检失败"
                    },
                    formData2: {
                        "groupId": "",
                        "taskId": "",
                        "data": "",
                        "except": "",
                        "enable_status": "01",
                        "agentPhone": "",
                        "roleId": ""
                    },
                    form: {},
                    form2: {},
                    pageNav: {
                        size: 15,
                        page: 1,
                        total: 0,
                        loading: false
                    },
                    pageNav2: {
                        size: 15,
                        page: 1,
                        total: 0,
                        loading: false
                    },
                    isAdmin: false,
                    tableData: [],
                    tableData2: [],
                    show: false,
                    pickerOptions: {
                        shortcuts: [{
                            text: getI18nValue('最近一周'),
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: getI18nValue('最近一个月'),
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: getI18nValue('最近三个月'),
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                                picker.$emit('pick', [start, end]);
                            }
                        }]
                    },
                    opt1: {
                        // 质检结果
                        // '': '全部',
                        '0': getI18nValue('不及格'),
                        '1': getI18nValue('及格')
                    },
                    ChannelTask: {},
                    props: {
                        label: 'LABEL',
                        value: 'VALUE'
                    },


                    TASKBYCHANNEL: {},
                    QC_CHANNEL_TYPE: {},
                    roleDict: {},


                    THIRD_PARTY_SOURCE: {
                        "1": getI18nValue("手工导入"),
                        "2": getI18nValue("接口导入")
                    },
                    ids: [],
                    inspectorAcc: [],
                    selectList: [],

                    sels: [],
                    cols: [],
                    THIRD_PARTY_BUSI_TYPE: {},
                    THIRD_PARTY_BUSI_DIRECTION: {},
                    THIRD_PARTY_SOURCE: {},
                    conditionDialogVisible: false,
                    conditionForm: {
                        type: '1',
                        inspectorArray: [{
                            inspectorAcc: '',
                            nums: 1,
                            rate: 0
                        }]
                    },
                    inspectorList: [],

                    show_query_drawer: false,
                    znResultItemList: [],
                    TEMPLATE_LIST: {},
                    statQueryForm: {
                        isAsrInfo: '0',
                        znScoreStart: '',
                        znScoreEnd: '',
                    }
                }
            },
            computed: {
                isEmptyText() {
                    return this.cols.length > 0 ? getI18nValue('暂无数据') : getI18nValue('请选择任务')
                },
                selectedCount() {
                    let len = this.selectList.length
                    this.selectList.forEach(item => {
                        if (item.children && item.children.length) {
                            len += item.children.length
                        }
                    })
                    return len
                }
            },
            beforeCreate() {
            },
            created() {

            },
            watch: {
                'formData.date': {
                    handler(n) {
                        if (yq.isNull(n)) {
                            this.formData.beginStartDate = ''
                            this.formData.endStartDate = ''
                            return;
                        }
                        this.formData.beginStartDate = n[0]
                        this.formData.endStartDate = n[1]
                    }
                },
                'formData.channelType': {
                    handler(n) {
                        console.log("channelType")
                        this.formData.channelType = n;
                        this.getDict();
                    }
                }
            },
            methods: {
                resetStatQuery() {
                    this.$refs.zdy_query.resetFields()
                },
                queryByStat() {
                    if (!this.$refs.zdy_query.checkValid()) {
                        return;
                    }
                    this.show_query_drawer = false
                    this.statQueryForm = this.$refs.zdy_query.getForm()
                    this.doSearch()
                },
                showStatQuery() {
                    this.show_query_drawer = true
                },
                setRowClass(row, rowIndex) {
                    if (row.row && row.row.IS_HIDE) {
                        return 'is-hide'
                    }
                    return ''
                },
                toLink(type) {
                    switch (type) {
                        case 'BZ': {
                            top.popup.openTab({
                                url: '/cc-quality/pages/qualityControl/task/qc-media-allocation-list.html',
                                title: getI18nValue('名单分配'),
                            });
                        }; break;
                    }
                },
                // 任务选中列表
                handleSelectionChanges(selection) {
                    this.ids = selection.map(item => item.OBJ_ID)
                    this.selectList = selection
                    if (this.selectList.length > 0) {
                        this.selectList.forEach(item => {
                            if (item.children) {
                                item.children.forEach(child => {
                                    this.ids.push(child.OBJ_ID)
                                })
                            }
                        })
                    }
                },
                // 坐席选中列表
                handleSelectionChange(selection) {
                    if (selection.length > 1) {
                        this.$refs.tb.clearSelection();
                        this.$refs.tb.toggleRowSelection(selection.pop());
                    }
                    this.inspectorAcc = selection.map(item => item.USER_ACCT)

                },
                formatTimeBySecond(time) {
                    if (time == undefined || time == null || time == '') return "0:00:00";
                    time = parseInt(time);
                    var h = Math.floor(time / 3600);
                    var m = Math.floor(time % 3600 / 60);
                    var s = time % 60;
                    m = m < 10 ? '0' + m : m;
                    s = s < 10 ? '0' + s : s;
                    return h + ":" + m + ":" + s;
                },
                handleSuccess(response, file, fileList) {
                    // 判断状态码是否在成功范围内
                    if (response.state === '1') {
                        // 处理上传成功情况
                        this.$message({
                            message: getI18nValue('上传成功'),
                            type: 'success'
                        });
                        this.dialogFormVisible = false
                        this.fileList = []
                        this.doSearch()
                    } else {
                        this.$message({
                            message: getI18nValue('上传失败') + '：' + response.msg,
                            type: 'error'
                        });
                    }
                },
                doSearch() {
                    // 检查是否选择了模板
                    if (!this.formData.templateId) {
                        this.$message.warning(getI18nValue('请先选择模板！'))
                        return
                    }

                    this.pageNav.loading = true
                    this.selectList = []
                    var form = Object.fromEntries(Object.entries(this.formData).filter(([key, value]) => {
                        // 过滤掉两个时间数组
                        return key != 'date' && key != 'date2';
                    }));
                    form.form.beginStartDate = this.formData.beginStartDate
                    form.form.endStartDate = this.formData.endStartDate
                    if(form.znItemIds && form.znItemIds.length > 0){
                        // 使用统一的处理方法
                        form.znItemIds = this.processZnItemIds(form.znItemIds);
                    }
                    var data = {
                        data: {
                            ...form,
                            templateId: this.formData.templateId,  // 确保templateId作为顶级参数传递
                            isadmin: true,
                            ...this.statQueryForm
                        },
                        pageSize: this.pageNav.size,
                        pageIndex: this.pageNav.page,
                        pageType: '3'
                    }
                    var that = this
                    yq.tableCall('/cc-quality/webcall?action=QcTaskObjDao.objThirdPartyList', data, function (res) {
                        if (res.state == 1) {
                            that.tableData = res.data
                            that.pageNav.total = res.totalRow
                            that.$message({
                                message: res.msg,
                                type: 'success'
                            });
                            // that.tableData = [
                            //     {
                            //         OBJ_ID: '1', children: [
                            //             { OBJ_ID: '2',IS_HIDE: '1' }, { OBJ_ID: '3',IS_HIDE: '1' }, { OBJ_ID: '4',IS_HIDE: '1' }
                            //         ]
                            //     }, { OBJ_ID: '5' }
                            // ]
                        } else {
                            that.$message({
                                message: res.msg,
                                type: 'error'
                            });
                        }
                    }).finally(res => {
                        that.pageNav.loading = false
                    })
                },
                reset() {
                    this.$refs.form.resetFields();
                },
                reset2() {
                    this.$refs.form2.resetFields();
                },
                doSearch2() {
                    this.pageNav2.loading = true
                    let data = {
                        data: this.formData2,
                        pageSize: this.pageNav.size,
                        pageIndex: this.pageNav.page,
                        pageType: '3'
                    }
                    let that = this
                    yq.tableCall('/cc-quality/webcall?action=QcGroupDao.inspectorList', data, function (res) {
                        if (res.state == 1) {
                            that.tableData2 = res.data
                            that.pageNav2.total = res.totalRow
                        }
                    }).finally(res => {
                        this.pageNav2.loading = false
                    })
                },
                handleCurrentChange(val) {
                    this.pageNav.page = val
                    this.doSearch()
                },
                handleSizeChange(val) {
                    this.pageNav.size = val
                    this.doSearch()
                },
                handleCurrentChange2(val) {
                    this.pageNav.page = val
                    this.doSearch()
                },
                handleSizeChange2(val) {
                    this.pageNav.size = val
                    this.doSearch()
                },
                getDict() {
                    var _this = this
                    let data = {
                        params: {},
                        controls: [
                            "common.getDict(THIRD_PARTY_BUSI_TYPE)", 'QcCommonDao.getTemplateId',
                            "common.getDict(THIRD_PARTY_BUSI_DIRECTION)",
                            "common.getDict(THIRD_PARTY_SOURCE)",
                            "QcCommonDao.getDict(QC_CHANNEL_TYPE)"
                        ]
                    }
                    yq.daoCall(data, null, {
                        "contextPath": "/cc-quality"
                    }).then(function (res) {//接口的方式渲染
                        _this.THIRD_PARTY_BUSI_TYPE = res['common.getDict(THIRD_PARTY_BUSI_TYPE)'].data
                        _this.THIRD_PARTY_BUSI_DIRECTION = res['common.getDict(THIRD_PARTY_BUSI_DIRECTION)'].data
                        _this.THIRD_PARTY_SOURCE = res['common.getDict(THIRD_PARTY_SOURCE)'].data
                        _this.QC_CHANNEL_TYPE = res["QcCommonDao.getDict(QC_CHANNEL_TYPE)"].data
                        _this.TEMPLATE_LIST = res['QcCommonDao.getTemplateId'].data
                    })
                },
                getDict2() {
                    var _this = this
                    let data = {
                        params: {},
                        controls: ["QcCommonDao.roleDict"]
                    }
                    yq.daoCall(data, null, {
                        "contextPath": "/cc-quality"
                    }).then(function (res) {
                        _this.roleDict = res["QcCommonDao.roleDict"].data
                    })
                },
                save() {
                    var _this = this
                    if (_this.inspectorAcc.length == 0) {
                        this.$message.warning(getI18nValue('至少选择一个质检员！'))
                        return;
                    }
                    let data = {
                        data: {
                            inspectorAcc: _this.inspectorAcc[0],
                            ids: _this.ids,
                            isTransfer: '',//判断当前是否转派操作
                            isMaDis: '',//判断当前是否手工分配
                            qcRecId: '',
                            qcResultId: ''
                        }
                    }
                    yq.tableCall('/cc-quality/servlet/QcTaskObj?action=reSetInspector', data, function (res) {
                        if (res.state == 1) {
                            _this.$message({
                                message: res.msg,
                                type: "success"
                            })
                            _this.doSearch()
                            _this.dialogFormVisible = false
                        } else {
                            _this.$message({
                                message: res.msg,
                                type: "error"
                            })
                        }
                    }).finally(res => {

                    })
                },
                // 批量分配
                changeInspectorAll() {
                    if (this.formData.taskId == '') {
                        this.$message.warning(getI18nValue('请先选择任务在进行批量分配！'))
                        return;
                    }
                    if (this.selectList.length == 0) {
                        this.$message.warning(getI18nValue('请先选择分配数据！'))
                        return;
                    }
                    // this.ids = [],
                    this.inspectorAcc = [],
                        this.dialogFormVisible = true
                    this.dialogTitle = getI18nValue("批量分配")
                    this.formData2.groupId = this.selectList[0].EXAM_GROUP_ID
                    this.getDict2()
                    this.doSearch2()
                },
                // 分配
                changeInspector(id, groupId, row) {
                    this.ids = [],
                        this.inspectorAcc = [],
                        this.dialogFormVisible = true
                    this.dialogTitle = getI18nValue("分配质检员")
                    this.formData2.groupId = groupId
                    this.getDict2()
                    this.doSearch2()
                    if (row.children) {
                        this.ids = row.children.map(item => item.OBJ_ID)
                        this.ids = [...this.ids, id]
                    } else {
                        this.ids = [id]
                    }
                },
                submitConditionDistribution() {
                    // 检查是否选中了记录
                    if (this.selectList.length === 0) {
                        this.$message.warning(getI18nValue('请先勾选需要分配的记录！'))
                        return
                    }

                    // 表单验证
                    const type = this.conditionForm.type
                    const inspectorArray = this.conditionForm.inspectorArray

                    // 检查是否选择了质检员
                    if (inspectorArray.some(item => !item.inspectorAcc)) {
                        this.$message.warning(getI18nValue('请选择质检员'))
                        return
                    }

                    // 按数量分配时的校验
                    if (type === '1') {
                        const totalNums = inspectorArray.reduce((sum, item) => sum + (item.nums || 0), 0)
                        if (totalNums > this.selectedCount) {
                            this.$message.warning(getI18nValue('分配总数不能超过选中记录数') + `(${this.selectedCount})`)
                            return
                        }
                        if (totalNums < this.selectedCount) {
                            this.$message.warning(getI18nValue('分配总数需等于选中记录数') + `(${this.selectedCount})`)
                            return
                        }
                    }

                    // 按比例分配时的校验
                    if (type === '2') {
                        const totalRate = inspectorArray.reduce((sum, item) => sum + (item.rate || 0), 0)
                        if (totalRate !== 100) {
                            this.$message.warning(getI18nValue('分配比例总和必须为100%'))
                            return
                        }
                    }

                    // 调用分配接口
                    let data = JSON.parse(JSON.stringify({
                        ids: this.ids,
                        type: this.conditionForm.type,
                        inspectorArray: this.conditionForm.inspectorArray
                    }))
                    // 按比例分配时，将rate转成小数
                    if (this.conditionForm.type === '2') {
                        data.inspectorArray.forEach(item => {
                            item.rate = item.rate / 100
                            item.rate = item.rate.toFixed(2)
                        })
                    }
                    yq.remoteCall('/cc-quality/servlet/QcTaskObj?action=DynamicDistribution', data, (res) => {
                        if (res.state == 1) {
                            this.$message.success(res.msg || getI18nValue('分配成功'))
                            this.conditionDialogVisible = false
                            this.doSearch()
                        } else {
                            this.$message.error(res.msg || getI18nValue('分配失败'))
                        }
                    })
                },
                goDetail(msg) {
                    // 查看详情
                    this.resultData(msg.TEMPLATE_ID, msg.OBJ_ID, msg.QC_RESULT_ID, msg.SERIAL_ID, msg.CALL_TYPE, msg.ZN_CLASS_ID, msg.EXAM_GROUP_ID, msg.CLASS_ID, msg.TASK_ID, msg.CHANNEL_TYPE)
                },
                resultData(templateId, objId, qcResultId, serialId, callType, znClassId, groupId, classId, taskId, channelType) {
                    var data = { objId: objId, qcResultId: qcResultId, serialId: serialId, znClassId: znClassId, templateId: templateId };
                    if (channelType == 1) {
                        let params = Object.keys(data).map(key => `${key}=${data[key]}`).join('&');
                        yq.popup.openTab({
                            url: `/cc-quality/pages/qualityControl/result/qc-result-voice-detail.html?${params}`,
                            title: getI18nValue('质检详情'),
                            id: 'qc-result-voice-detail-' + serialId
                        });
                    } else if (channelType == 3) {
                        data.isRead = true;
                        let params = Object.keys(data).map(key => `${key}=${data[key]}`).join('&');
                        yq.popup.openTab({
                            url: `/cc-quality/pages/qualityControl/result/qc-result-email-detail.html?${params}`,
                            title: getI18nValue('质检详情'),
                            id: 'qc-result-email-detail-' + serialId
                        });
                    } else if (channelType == 4) {
                        data = { objId: objId, znClassId: znClassId, serialId: serialId, groupId: groupId, taskId: taskId, isRead: true };
                        let params = Object.keys(data).map(key => `${key}=${data[key]}`).join('&');
                        yq.popup.openTab({
                            url: `/cc-quality/pages/qualityControl/result/qc-result-order-detail.html?${params}`,
                            title: getI18nValue('质检详情'),
                            id: 'qc-result-order-detail-' + serialId
                        });
                    } else if (channelType == 9) {
                        var thirdData = { templateId: templateId, groupId: groupId, serialId: serialId, objId: objId, classId: classId, znClassId: znClassId, qcResultId: qcResultId };
                        let params = Object.keys(thirdData).map(key => `${key}=${thirdData[key]}`).join('&');
                        yq.popup.openTab({
                            url: `/cc-quality/pages/qualityControl/result/qc-result-thrity-detail.html?${params}`,
                            title: getI18nValue('质检详情'),
                            id: 'qc-result-thrity-detail-' + serialId
                        });
                    } else {
                        let params = Object.keys(data).map(key => `${key}=${data[key]}`).join('&');
                        yq.popup.openTab({
                            url: `/cc-quality/pages/qualityControl/result/qc-result-media-detail.html?${params}`,
                            title: getI18nValue('质检详情'),
                            id: 'qc-result-media-detail-' + serialId
                        });
                    }
                },
                getTaskList(id){
                    this.cols = []
                    this.sels = []
                    yq.remoteCall('/cc-quality/webcall?action=QcThirdPartyDao.getTemplateTaskList', {
                        TEMPLATE_ID: id
                    }).then(res => {
                       this.TASKBYCHANNEL = res.data
                    })
                    this.getOptions(id)
                },
                getZnRuleItems(){
                    yq.daoCall({
                        params: {
                            taskId: this.formData.taskId
                        },
                        controls: ["QcClassDao.znResultItemList"]
                    }, null, {
                        "contextPath": "/cc-quality"
                    }).then((res) => {
                        if (res["QcClassDao.znResultItemList"] && res["QcClassDao.znResultItemList"].data) {
                            this.znResultItemList = res["QcClassDao.znResultItemList"].data
                        }
                    })
                    this.getOptions(id)
                },
                getOptions(id) {
                    this.pageNav.loading = true
                    let data = {
                        templateId: id,
                    }
                    yq.remoteCall('/cc-quality/webcall?action=QcTaskObjDao.objThirdPartyTemplate', data).then(res => {
                        this.cols = res.itemOptions
                        this.sels = res.selectOptions
                        if (this.sels.length > 0) {
                            this.sels.forEach((item, index) => {
                                this.$set(this.formData.form, item.id, '')
                                if (item.showType == '4') {
                                    this.getTree(item.url, (tree) => {
                                        this.$set(this.sels[index], 'tree', tree)
                                    })
                                }
                            })
                        }
                        setTimeout(() => {
                            this.$refs.table.doLayout()
                            this.doSearch()
                        }, 500)
                    })
                },
                getTree(url, callback) {
                    yq.remoteCall(url, {}).then(res => {
                        console.log('getTree', res);
                        callback([res.data])
                    })
                },
                // 处理质检规则ID数组
                processZnItemIds(znItemIds) {
                    let processedIds = [];
                    if (znItemIds && znItemIds.length > 0) {
                        znItemIds.forEach((item) => {
                            try{
                                if (Array.isArray(item) && item.length > 0) {
                                    // 取数组的最后一个元素作为质检项ID
                                    processedIds.push(item[item.length - 1]);
                                } else if (typeof item === 'string') {
                                    // 如果已经是字符串，直接使用
                                    processedIds.push(item);
                                }
                            }catch(e){
                                console.error('处理znItemIds出错:', e);
                            }
                        });
                    }
                    return processedIds;
                },
                showConditionDialog() {
                    if (this.formData.taskId == '') {
                        this.$message.warning(getI18nValue('请先选择任务在进行条件分配！'))
                        return
                    }
                    // if (this.selectList.length == 0) {
                    //     this.$message.warning(getI18nValue('请先勾选任务！'))
                    //     return;
                    // }
                    this.conditionDialogVisible = true
                    this.getInspectorList() // 获取质检员列表
                },
                getInspectorList() {
                    let data = {
                        data: {
                            enable_status: "01",
                            taskId: this.formData.taskId
                        },
                        pageSize: 999,
                        pageIndex: 1
                    }
                    yq.tableCall('/cc-quality/webcall?action=QcGroupDao.inspectorList', data, (res) => {
                        if (res.state == 1) {
                            this.inspectorList = res.data
                        }
                    })
                },
                addInspector() {
                    this.conditionForm.inspectorArray.push({
                        inspectorAcc: '',
                        nums: 1,
                        rate: 0
                    })
                },
                removeInspector(index) {
                    this.conditionForm.inspectorArray.splice(index, 1)
                },
                submitConditionDistribution() {
                    // 检查是否选中了记录
                    if (this.selectList.length === 0) {
                        this.$message.warning(getI18nValue('请先勾选需要分配的记录！'))
                        return
                    }

                    // 表单验证
                    const type = this.conditionForm.type
                    const inspectorArray = this.conditionForm.inspectorArray

                    // 检查是否选择了质检员
                    if (inspectorArray.some(item => !item.inspectorAcc)) {
                        this.$message.warning(getI18nValue('请选择质检员'))
                        return
                    }

                    // 按数量分配时的校验
                    if (type === '1') {
                        const totalNums = inspectorArray.reduce((sum, item) => sum + (item.nums || 0), 0)
                        if (totalNums > this.selectedCount) {
                            this.$message.warning(getI18nValue('分配总数不能超过选中记录数') + `(${this.selectedCount})`)
                            return
                        }
                        if (totalNums < this.selectedCount) {
                            this.$message.warning(getI18nValue('分配总数需等于选中记录数') + `(${this.selectedCount})`)
                            return
                        }
                    }

                    // 按比例分配时的校验
                    if (type === '2') {
                        const totalRate = inspectorArray.reduce((sum, item) => sum + (item.rate || 0), 0)
                        if (totalRate !== 100) {
                            this.$message.warning(getI18nValue('分配比例总和必须为100%'))
                            return
                        }
                    }

                    // 调用分配接口
                    let data = JSON.parse(JSON.stringify({
                        ids: this.ids,
                        type: this.conditionForm.type,
                        inspectorArray: this.conditionForm.inspectorArray
                    }))
                    // 按比例分配时，将rate转成小数
                    if (this.conditionForm.type === '2') {
                        data.inspectorArray.forEach(item => {
                            item.rate = item.rate / 100
                            item.rate = item.rate.toFixed(2)
                        })
                    }
                    yq.remoteCall('/cc-quality/servlet/QcTaskObj?action=DynamicDistribution', data, (res) => {
                        if (res.state == 1) {
                            this.$message.success(res.msg || getI18nValue('分配成功'))
                            this.conditionDialogVisible = false
                            this.doSearch()
                        } else {
                            this.$message.error(res.msg || getI18nValue('分配失败'))
                        }
                    })
                }
            },
            mounted: function () {
                let that = this
                // yq.remoteCall("/cc-quality/servlet/qcRecord?action=isAdmin", {}, function (result) {
                //     if (result.state == 1) {
                //         that.isAdmin = true
                //     }
                // });
                // 获取今天日期和30天前的日期
                const today = new Date();
                const prevDate = new Date(today);
                prevDate.setDate(today.getDate() - 30);

                const formatDate = date => {
                    return date.getFullYear() + '-' +
                        String(date.getMonth() + 1).padStart(2, '0') + '-' +
                        String(date.getDate()).padStart(2, '0');
                };

                const formattedToday = formatDate(today);
                const formattedPrevDate = formatDate(prevDate);

                this.formData.date = [formattedPrevDate, formattedToday];
                this.formData.beginStartDate = formattedPrevDate;
                this.formData.endStartDate = formattedToday;
                this.getDict();
                // this.doSearch()
            }
        })
    </script>
</body>

</html>