<!DOCTYPE html>
<html>

<head>
    <title>智能质检结果</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport"
          content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <!-- 基础的 css js 资源 -->
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0">
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0">
    <link rel="stylesheet" href="/cc-quality/static/css/qualityControl.css?v=1.0.1">
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
    <script src="/cc-quality/static/lib/wavesurfer/wavesurfer.js"></script>
    <script src="/cc-quality/static/lib/wavesurfer/wavesurfer.timeline.min.js"></script>
    <script type="text/javascript" src="/cc-quality/static/js/my_i18n.js?v=2021110901"></script>
    <script type="text/javascript" src="/cc-base/static/js/i18n.js?v=20140426"></script>

    <style>
        .yq-table-page {
            background-color: #fff;
        }

        .title-box {
            padding: 16px;
            border-bottom: 1px solid #E8E8E8;
            display: flex;
            justify-content: space-between;
        }

        .search-box {
            padding: 10px 24px;
        }

        .el-form.el-form--inline .el-input__inner {
            height: auto !important;
            line-height: auto !important;
            background: #f2f4f7;
        }

        .table-box {
            padding: 24px;
            padding-top: 0;
        }

        .reset-form-btn {
            position: absolute;
            left: 24px;
            color: #0555CE;
            cursor: pointer;
        }
    </style>
</head>

<body class="yq-page-full vue-box">
<div id="qcZnResultList" class="flex yq-table-page" element-loading-text="加载中..." v-cloak>
    <div class="flex yq-card">
        <div class="title-box">
            <div class="title">{{getI18nValue('质检结果')}}

                <el-popover placement="bottom" title="" width="500" trigger="hover" content="">
                    <div>
                        <p><span>{{getI18nValue("说明")}}</span>
                        <hr>
                        </p>
                        <p>{{getI18nValue("备注:仅显示智能质检结果")}}</p>
                    </div><i class="el-icon-question" slot="reference"></i>
                </el-popover>
            </div>
            <div>
                <!-- <el-button size="small" type="primary">质检结果明细</el-button> -->
                <el-button size="small" type="primary" plain
                           @click="showStatQuery">{{getI18nValue('统计指标')}}</el-button>
                <el-button size="small" type="primary" @click="expResult">{{getI18nValue('导出')}}</el-button>
<!--                <el-tooltip class="item" effect="dark" content="能够导出智能质检结果的详细信息" placement="bottom">-->
<!--                    <el-button size="small" type="primary"-->
<!--                               @click="expResultOffline">{{getI18nValue('离线导出')}}</el-button>-->
<!--                </el-tooltip>-->
            </div>
        </div>
        <div class="card-content">
            <senior-search :show.sync="show">
                <el-form :inline="false" :model="formData" ref="form" id="searchForm" class="search-form"
                         label-width="100px">
                    <el-form-item :label="getI18nValue('关键字')" prop="keyword">
                        <el-input v-model="formData.keyword" :placeholder="getI18nValue('关键字')" size="small"
                                  clearable></el-input>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('质检时间')" prop="date">
                        <el-date-picker v-model="formData.date" type="daterange" align="right" unlink-panels
                                        range-separator="至" size="small" :start-placeholder="getI18nValue('开始日期')"
                                        :end-placeholder="getI18nValue('结束日期')" :picker-options="pickerOptions"
                                        value-format="yyyy-MM-dd" clearable>
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('模板')" prop="templateId">
                        <el-select v-model="formData.templateId" :placeholder="getI18nValue('模板')" clearable
                                   @change="getTaskList">
                            <el-option v-for="(key,value) of templateList" :label="key" :value="value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('任务名称')" prop="taskId" v-show="show">
                        <el-select v-model="formData.taskId" :placeholder="getI18nValue('任务名称')" clearable
                                   filterable @change="getZNResultItemList">
                            <el-option v-for="(key,value) of TASKBYCHANNEL" :label="key" :value="value"></el-option>
                        </el-select>
                    </el-form-item>
                    <!--质检对象表中的服务时间对应源记录表中的创建时间-->
                    <el-form-item :label="getI18nValue('服务时间')" prop="date2" v-show="show">
                        <el-date-picker v-model="formData.date2" type="daterange" value-format="yyyy-MM-dd"
                                        size="small" clearable :start-placeholder="getI18nValue('开始日期')"
                                        :end-placeholder="getI18nValue('结束日期')" :picker-options="pickerOptions"
                                        :unlink-panels="true">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('质检坐席')" prop="agentName" v-show="show">
                        <el-input v-model="formData.agentName" :placeholder="getI18nValue('质检坐席')" size="small"
                                  clearable></el-input>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('渠道类型')" prop="callType" v-show="show">
                        <el-select v-model="formData.callType" :placeholder="getI18nValue('服务类型')" clearable
                                   size="small">
                            <el-option v-for="(key,value) of QC_CHANNEL_TYPE" :label="key"
                                       :value="value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('业务类型')" prop="busiType" v-show="show">
                        <el-select v-model="formData.busiType" :placeholder="getI18nValue('业务类型')" clearable
                                   size="small">
                            <el-option v-for="(key,value) of busyTypeOptions" :label="key"
                                       :value="value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="getI18nValue('质检规则')" prop="znItemIds" v-show="show">
                        <el-cascader :options="znResultItemList" style="width: 100%;" v-model="formData.znItemIds"
                                     :props="{ multiple: true, checkStrictly: true, value: 'itemId', label: 'itemName' }"
                                     clearable></el-cascader>
                    </el-form-item>
                    <!-- 动态搜索条件 -->
                    <el-form-item v-for="(item,index) in sels" :key="'sel'+index" :label="getI18nValue(item.label)"
                        :prop="item.id" :label-width="(item.searchWidth || 100)+'px'" v-show="show">
                        <template v-if="item.showType=='1'">
                            <el-input v-model="formData.form[item.id]" :placeholder="getI18nValue(item.label)"
                                clearable></el-input>
                        </template>
                        <template v-else-if="item.showType=='2'">
                            <el-select v-model="formData.form[item.id]" :placeholder="getI18nValue(item.label)"
                                clearable style="width: 100%;">
                                <el-option v-for="(tem,dex) in item.arr" :label="tem.value"
                                    :value="tem.key"></el-option>
                            </el-select>
                        </template>
                        <template v-else-if="item.showType=='3'">
                            <el-date-picker v-model="formData.form[item.id]"
                                :type="item.dateType=='date'? 'date' : 'datetime'" style="width:100%" align="right"
                                unlink-panels range-separator="~" :start-placeholder="getI18nValue('开始时间')"
                                :end-placeholder="getI18nValue('结束时间')" :placeholder="getI18nValue(item.label)"
                                :value-format="item.format" clearable>
                            </el-date-picker>
                        </template>
                        <template v-else-if="item.showType=='4'">
                            <el-cascader v-model="formData.form[item.id]" :options="item.tree" :props="bs_prop"
                                style="width:100%" :placeholder="getI18nValue(item.label)" clearable></el-cascader>
                        </template>
                    </el-form-item>
                    <el-form-item class="btns" label-width="0px">
                        <el-button type="primary" size="small" icon="el-icon-search"
                                   v-debounce="() => doSearch(1)">{{getI18nValue('查询')}}</el-button>
                        <el-button type="primary" plain size="small" icon="el-icon-refresh"
                                   @click="reset">{{getI18nValue('重置')}}</el-button>
                        <el-button type="primary" plain size="small" icon="el-icon-arrow-down"
                                   @click="show=!show">{{getI18nValue('高级查询')}}</el-button>
                    </el-form-item>
                </el-form>
            </senior-search>
            <div class="yq-table">
                <el-table :data="tableData" style="width: 100%" height="100%" ref="table" v-loading="pageNav.loading">
                    <el-table-column type="index" :label="getI18nValue('序号')" width="50" fixed></el-table-column>
                    <el-table-column prop="TASK_NAME" :label="getI18nValue('任务名称')" min-width="120"
                                     show-overflow-tooltip></el-table-column>
                    <el-table-column prop="AGENT_NAME" :label="getI18nValue('质检坐席')" min-width="120"
                                     show-overflow-tooltip></el-table-column>
                    <el-table-column prop="SCORE" :label="getI18nValue('智能质检分数')" min-width="120"
                                     show-overflow-tooltip></el-table-column>
                    <el-table-column prop="CHANNEL_TYPE" :label="getI18nValue('渠道类型')" min-width="80"
                                     show-overflow-tooltip>
                        <template slot-scope="scope">
                            {{QC_CHANNEL_TYPE[scope.row.CHANNEL_TYPE]}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="BUSI_TYPE" :label="getI18nValue('业务类型')" min-width="100">
                        <template slot-scope="scope">
                            {{ busyTypeOptions[scope.row.BUSI_TYPE] || '--'}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="QC_TIME" :label="getI18nValue('质检时间')" min-width="150"
                                     show-overflow-tooltip></el-table-column>

                    <!-- 自定义配置 -->
                    <template v-for="(item,index) in cols">
                        <el-table-column :prop="item.key" :label="getI18nValue(item.title)" min-width="180"
                                         :key="index" show-overflow-tooltip v-if="item.key=='THIRD_BUSI_TYPE'">
                            <template slot-scope="scope">{{THIRD_PARTY_BUSI_TYPE[scope.row[item.key]]}}</template>
                        </el-table-column>
                        <el-table-column :prop="item.key" :label="getI18nValue(item.title)" min-width="180"
                                         :key="index" show-overflow-tooltip v-else-if="item.key=='THIRD_BUSI_DIRECTION'">
                            <template
                                    slot-scope="scope">{{THIRD_PARTY_BUSI_DIRECTION[scope.row[item.key]]}}</template>
                        </el-table-column>
                        <el-table-column :prop="item.key" :label="getI18nValue(item.title)" min-width="180"
                                         :key="index" show-overflow-tooltip v-else-if="item.key=='THIRD_SOURCE'">
                            <template slot-scope="scope">{{THIRD_PARTY_SOURCE[scope.row[item.key]]}}</template>
                        </el-table-column>
                        <el-table-column :prop="item.key" :label="getI18nValue(item.title)" min-width="180"
                                         :sortable="item.dateType == 'time'" :key="index" show-overflow-tooltip v-else>
                        </el-table-column>
                    </template>

                    <el-table-column prop="opt" :label="getI18nValue('操作')" width="200" fixed="right">
                        <template slot-scope="scope">
                            <el-link type="primary" szie="small" v-if="yq.isNull(scope.row.SELECT_ID)"
                                     @click="goDetail(scope.row)" :underline="false">{{getI18nValue('查看详情')}}</el-link>
                            <el-link type="primary" szie="small" @click="handleCheck(scope.row)"
                                     :underline="false">{{getI18nValue('复检')}}</el-link>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination class="no-pager" background @current-change="handleCurrentChange"
                               @size-change="handleSizeChange" :current-page="pageNav.page" :page-sizes="[15, 30, 50, 100]"
                               :page-size="pageNav.size" layout="slot, sizes">
                    <span class="el-pagination__total">{{pageNav.total}}条</span>
                    <el-button size="small" type="primary" icon="el-icon-arrow-left"
                               @click="handlePrevPage"></el-button>
                    <el-button size="small" type="primary" icon="el-icon-arrow-right"
                               @click="handleNextPage"></el-button>
                </el-pagination>
            </div>
        </div>
        <!-- 统计指标 -->
        <el-drawer title="统计指标" :visible.sync="show_query_drawer" size="50%" :wrapper-closable="false">
            <div class="drawer-content">
                <zdy-query ref="zdy_query"></zdy-query>
            </div>
            <div class="drawer-footer">
                <div class="reset-form-btn" @click="resetStatQuery">
                    <i class="el-icon-refresh"></i> {{getI18nValue('重置')}}
                </div>
                <el-button type="primary" plain
                           @click="()=>{$refs.zdy_query.resetFields();show_query_drawer=false}">{{getI18nValue('关闭')}}</el-button>
                <el-button type="primary" @click="queryByStat">{{getI18nValue('查询')}}</el-button>
            </div>
        </el-drawer>
    </div>

</div>

<script>
    // const iframeComm = new IframeCommunication();
    // 每个页面的容器id和实例变量要不一样
    var qc_zn_resultz_list = new Vue({
        components: { //加载组件
            // 'orderMainFrame': httpVueLoader('comp/orderMainFrame.vue')
            'zdy-query': httpVueLoader('/cc-quality/pages/qualityControl/components/zdy_query.vue')
        },
        el: '#qcZnResultList',
        data: function () {
            return {
                formData: {
                    date: '',
                    keyword: '',
                    date2: '',
                    cachResult: '',
                    templateId: '',
                    form:{},
                    oneVoteItem: '',
                    callType: '',
                    passFlag: '',
                    objType: '',
                    agentName: '',
                    taskId: '',
                    endServiceTime: '',
                    beginServiceTime: '',
                    endStartDate: '',
                    beginStartDate: '',
                    expSortType: "",
                    isInspector: "",
                    dateRange: "",
                    dateRangeServiceTime: "",
                    approvalState: "",
                    busiType: '',
                    znItemIds:[]
                },
                pageNav: {
                    size: 15,
                    page: 1,
                    total: 0,
                    loading: false
                },
                templateList: {},
                THIRD_PARTY_BUSI_TYPE: {},
                THIRD_PARTY_BUSI_DIRECTION: {},
                THIRD_PARTY_SOURCE: {},
                busyTypeOptions: {},
                TASKBYCHANNEL: {},
                isAdmin: false,
                tableData: [],
                cols: [],
                sels: [],
                show: false,
                bs_prop: {
                    value: 'id',
                    label: 'name',
                    children: 'children'
                },
                pickerOptions: {
                    shortcuts: [{
                        text: getI18nValue('最近一周'),
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: getI18nValue('最近一个月'),
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: getI18nValue('最近三个月'),
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                opt1: {
                    // 质检结果
                    // '': '全部',
                    '0': getI18nValue('不及格'),
                    '1': getI18nValue('及格')
                },
                ChannelTask: {},
                props: {
                    label: 'LABEL',
                    value: 'VALUE'
                },
                QC_CHANNEL_TYPE: {},
                QUALITY_COACHING_RESULT: {},
                opt2: {
                    // 申诉结果
                    // '': '全部',
                    '0': getI18nValue('未申诉'),
                    '1': getI18nValue('第一次申诉'),
                    '2': getI18nValue('第二次申诉'),
                    '5': getI18nValue('结案'),
                },
                opt3: {
                    // 案例类型
                    // '': '全部',
                    '1': getI18nValue('优秀案例'),
                    '2': getI18nValue('警示案例'),
                },
                opt4: {
                    // 服务类型
                    // '': '全部',
                    'Y': getI18nValue('是'),
                    'N': getI18nValue('否'),
                },
                show_query_drawer: false,
                znResultItemList: [],
                statQueryForm: {
                    isAsrInfo: '0',
                    znScoreStart: '',
                    znScoreEnd: '',
                }
            }
        },
        computed: {

        },
        beforeCreate() {
        },
        created() {

        },
        watch: {
            'formData.date': {
                handler(n) {
                    if (yq.isNull(n) || JSON.stringify(n) == '[]') {
                        this.formData.beginStartDate = ''
                        this.formData.endStartDate = ''
                        return;
                    }
                    this.formData.beginStartDate = n[0]
                    this.formData.endStartDate = n[1]
                }
            },
            'formData.date2': {
                handler(n) {
                    if (yq.isNull(n) || JSON.stringify(n) == '[]') {
                        this.formData.beginServiceTime = ''
                        this.formData.endServiceTime = ''
                        return;
                    }
                    this.formData.beginServiceTime = n[0]
                    this.formData.endServiceTime = n[1]
                }
            }
        },
        methods: {
            getOptions() {
                // 清空之前的动态字段
                this.clearDynamicFields();

                this.pageNav.loading = true;
                let data = {
                    templateId: this.formData.templateId,
                };

                yq.remoteCall('/cc-quality/webcall?action=QcTaskObjDao.objThirdPartyTemplate', data).then(res => {
                    console.log('API返回数据:', res); // 添加调试日志

                    this.cols = res.itemOptions || [];
                    this.sels = res.selectOptions || [];

                    console.log('设置的sels数据:', this.sels); // 添加调试日志

                    // 确保 formData.form 已初始化
                    if (!this.formData.form) {
                        this.$set(this.formData, 'form', {});
                    }

                    if (this.sels.length > 0) {
                        this.sels.forEach((item, index) => {
                            this.$set(this.formData.form, item.id, '');
                            if (item.showType == '4') {
                                this.getTree(item.url, (tree) => {
                                    this.$set(this.sels[index], 'tree', tree);
                                });
                            }
                        });
                    }

                    // 延迟执行表格布局和搜索
                    setTimeout(() => {
                        if (this.$refs.table && typeof this.$refs.table.doLayout === 'function') {
                            this.$refs.table.doLayout();
                        } else {
                            console.warn('this.$refs.table 不存在或未挂载');
                        }
                        this.doSearch();
                    }, 500);
                }).catch(error => {
                    console.error('获取模板配置失败:', error);
                }).finally(() => {
                    this.pageNav.loading = false;
                });
            },
            getTree(url, callback) {
                yq.remoteCall(url, {}).then(res => {
                    console.log('getTree', res);
                    callback([res.data]);
                }).catch(error => {
                    console.error('获取树形数据失败:', error);
                    callback([]);
                });
            },
            clearDynamicFields() {
                this.sels = [];
                this.formData.form = {};
            },
            // 处理质检规则ID数组
            processZnItemIds(znItemIds) {
                let processedIds = [];
                if (znItemIds && znItemIds.length > 0) {
                    znItemIds.forEach((item) => {
                        try{
                            if (Array.isArray(item) && item.length > 0) {
                                // 取数组的最后一个元素作为质检项ID
                                processedIds.push(item[item.length - 1]);
                            } else if (typeof item === 'string') {
                                // 如果已经是字符串，直接使用
                                processedIds.push(item);
                            }
                        }catch(e){
                            console.error('处理znItemIds出错:', e);
                        }
                    });
                }
                return processedIds;
            },
            // 通过模板ID获取任务列表
            getTaskList(id) {
                // 清空之前的数据
                this.cols = [];
                this.sels = [];
                this.TASKBYCHANNEL = {};
                this.znResultItemList = [];
                this.formData.taskId = ''; // 清空任务选择

                yq.remoteCall('/cc-quality/webcall?action=QcThirdPartyDao.getTemplateTaskList', {
                    TEMPLATE_ID: id
                }).then(res => {
                    this.TASKBYCHANNEL = res.data;
                }).catch(error => {
                    console.error('获取任务列表失败:', error);
                });

                // 获取模板配置
                this.getOptions(id);
            },
            // 获取质检规则数据（当任务选择变化时调用）
            getZNResultItemList() {
                // 清空之前的质检规则数据
                this.znResultItemList = [];

                if (!this.formData.taskId) {
                    console.warn('未选择任务，无法获取质检规则');
                    return;
                }

                yq.daoCall({
                    params: {
                        taskId: this.formData.taskId
                    },
                    controls: ["QcClassDao.znResultItemList"]
                }, null, {
                    "contextPath": "/cc-quality"
                }).then((res) => {
                    if (res["QcClassDao.znResultItemList"] && res["QcClassDao.znResultItemList"].data) {
                        this.znResultItemList = res["QcClassDao.znResultItemList"].data;
                    }
                }).catch(error => {
                    console.error('获取质检规则失败:', error);
                });
            },
            resetStatQuery() {
                this.$refs.zdy_query.resetFields()
            },
            queryByStat() {
                if (!this.$refs.zdy_query.checkValid()) {
                    return;
                }
                this.show_query_drawer = false
                this.statQueryForm = this.$refs.zdy_query.getForm()
                this.doSearch()
            },
            showStatQuery() {
                this.show_query_drawer = true
            },
            expResult() {
                // 检查是否选择了模板
                if (!this.formData.templateId) {
                    this.$message.warning('请先选择模板');
                    return;
                }

                var data = this.formData
                var s = "expSortName=" + (data.expSortName || '') + "&" +
                    "expSortType=" + (data.expSortType || '') + "&" +
                    "isInspector=" + (data.isInspector || '') + "&" +
                    "beginStartDate=" + (data.beginStartDate || '') + "&" +
                    "endStartDate=" + (data.endStartDate || '') + "&" +
                    "dateRange=" + (data.dateRange || '') + "&" +
                    "task=" + (data.task || '') + "&" +
                    "agentName=" + (data.agentName || '') + "&" +
                    "beginServiceTime=" + (data.beginServiceTime || '') + "&" +
                    "endServiceTime=" + (data.endServiceTime || '') + "&" +
                    "dateRangeServiceTime=" + (data.dateRangeServiceTime || '') + "&" +
                    "passFlag=" + (data.passFlag || '') + "&" +
                    "callType=" + (data.callType || '') + "&" +
                    "oneVoteItem=" + (data.oneVoteItem || '') + "&" +
                    "cachResult=" + (data.cachResult || '') + "&" +
                    "approvalState=" + (data.approvalState || '') + "&" +
                    "templateId=" + data.templateId + "&" +
                    "busiType=" + (data.busiType || '') + "&" +
                    "znItemIds=" + (data.znItemIds ? this.processZnItemIds(data.znItemIds).join(',') : '');

                // 添加动态表单字段
                if (data.form) {
                    for (let key in data.form) {
                        if (data.form[key]) {
                            s += "&form." + key + "=" + encodeURIComponent(data.form[key]);
                        }
                    }
                }

                location.href = "/cc-quality/servlet/qcExport?action=exportQcZnResultByTemplate&" + s;
            },
            //离线导出方法
            expResultOffline() {
                //跳转离线导出申请
                var param = {};
                param = this.formData;
                param.reportName = getI18nValue("智能质检结果");
                param.expCommand = "ZN_RESULT_LIST";
                typeof param.task == "object" ? this.$set(param, 'task', param?.task?.join(',') || '') : ''

                var params = {};
                params['EXP_PARAM'] = param;
                yq.remoteCall("/cc-quality/servlet/offlineExport?action=saveZnResultList", params, function (result) {
                    if (result.state == 1) {
                        var exportUrl = "/cc-base/pages/offlineExport/myOfflineExport.jsp";
                        // layer.alert(getI18nValue(result.msg)+','+getI18nValue('可点击')+' <a style="color:blue" href="javascript:void(0);" onclick="top.popup.layerShow({type:2,title:\''+getI18nValue("导出列表")+'\',area: [\'80%\', \'80%\']},\''+exportUrl+'\',{});layer.closeAll();">'+getI18nValue('导出列表')+'</a> '+getI18nValue('查看导出文件'),
                        // {icon: 1, btn: [getI18nValue('关闭')],btnAlign:'c', title:getI18nValue('提示'),offset:'20px',shade:0,zIndex: 3}, function(index){
                        //   layer.closeAll();
                        // });
                        layer.alert(getI18nValue(result.msg) + ',' + getI18nValue('可点击') + ' <a style="color:blue" href="javascript:void(0);" onclick="top.popup.openTab(\'' + exportUrl + '\',\'' + getI18nValue("导出列表") + '\', {});">' + getI18nValue('导出列表') + '</a> ' + getI18nValue('查看导出文件'), { icon: 1, btn: [getI18nValue('关闭')], btnAlign: 'c', title: getI18nValue('提示'), offset: '20px', shade: 0, zIndex: 3 }, function (index) {
                            layer.closeAll();
                        });
                    } else {
                        layer.msg(getI18nValue(result.msg), { icon: 5 });
                    }
                });
                // if("1"==offCheck||1==offCheck){
                // popup.layerShow({type:2,title:getI18nValue('离线导出申请原因'),offset:'20px',area:['400px','300px']},"/cc-report/pages/offline/exportReason.jsp?"
                // +$("#searchForm").serialize()+"&agentIds="+agentId
                // +"&isExportChat="+isExportChat+"&reportName=" + getI18nValue("智能质检结果")+ "&expCommand=" + "ZN_RESULT_LIST",{});
                // }else{
                //
                // }
            },

            resultData(templateId, objId, qcResultId, serialId, callType, znClassId, groupId, classId, taskId, channelType) {
                var data = { objId: objId, qcResultId: qcResultId, serialId: serialId, znClassId: znClassId, templateId: templateId };
                if (channelType == 1) {
                    let params = Object.keys(data).map(key => `${key}=${data[key]}`).join('&');
                    yq.popup.openTab({
                        url: `/cc-quality/pages/qualityControl/result/qc-result-voice-detail.html?${params}`,
                        title: getI18nValue('质检详情'),
                        id: 'qc-result-voice-detail-' + serialId
                    });
                } else if (channelType == 3) {
                    data.isRead = true;
                    let params = Object.keys(data).map(key => `${key}=${data[key]}`).join('&');
                    yq.popup.openTab({
                        url: `/cc-quality/pages/qualityControl/result/qc-result-email-detail.html?${params}`,
                        title: getI18nValue('质检详情'),
                        id: 'qc-result-email-detail-' + serialId
                    });
                } else if (channelType == 4) {
                    data = { objId: objId, znClassId: znClassId, serialId: serialId, groupId: groupId, taskId: taskId, isRead: true };
                    let params = Object.keys(data).map(key => `${key}=${data[key]}`).join('&');
                    yq.popup.openTab({
                        url: `/cc-quality/pages/qualityControl/result/qc-result-order-detail.html?${params}`,
                        title: getI18nValue('质检详情'),
                        id: 'qc-result-order-detail-' + serialId
                    });
                } else if (channelType == 9) {
                    var thirdData = { templateId: templateId, groupId: groupId, serialId: serialId, objId: objId, classId: classId, znClassId: znClassId, qcResultId: qcResultId };
                    let params = Object.keys(thirdData).map(key => `${key}=${thirdData[key]}`).join('&');
                    yq.popup.openTab({
                        url: `/cc-quality/pages/qualityControl/result/qc-result-thrity-detail.html?${params}`,
                        title: getI18nValue('质检详情'),
                        id: 'qc-result-thrity-detail-' + serialId
                    });
                } else {
                    let params = Object.keys(data).map(key => `${key}=${data[key]}`).join('&');
                    yq.popup.openTab({
                        url: `/cc-quality/pages/qualityControl/result/qc-result-media-detail.html?${params}`,
                        title: getI18nValue('质检详情'),
                        id: 'qc-result-media-detail-' + serialId
                    });
                }
            },
            // 复检
            handleCheck(row) {
                const { TEMPLATE_ID, EXAM_GROUP_ID, SERIAL_ID, OBJ_ID, CLASS_ID, ZN_CLASS_ID, QC_RESULT_ID, TASK_ID, CHANNEL_TYPE } = row
                if (CHANNEL_TYPE == '1') {
                    // 语音
                    var data = { isZnCheck: 'Y', groupId: EXAM_GROUP_ID, serialId: SERIAL_ID, objId: OBJ_ID, classId: CLASS_ID, znClassId: ZN_CLASS_ID, qcResultId: QC_RESULT_ID, taskId: TASK_ID, templateId: TEMPLATE_ID, layer: 'checkLayer' };
                    let params = Object.keys(data).map(key => `${key}=${data[key]}`).join('&');
                    this.checkLayer = yq.layerShow({
                        title: getI18nValue('复检'),
                        offset: 't',// 如果去掉,就是默认居中
                        area: ['100%', '100%'],
                        type: 2,//2或者iframe为iframe方式打开弹层,1或div为div方式
                    }, `/cc-quality/pages/qualityControl/result/qc-result-voice-check.html?${params}`)
                } else if (CHANNEL_TYPE == '2') {
                    // 全媒体
                    var data = { isZnCheck: 'Y', groupId: EXAM_GROUP_ID, serialId: SERIAL_ID, objId: OBJ_ID, classId: CLASS_ID, znClassId: ZN_CLASS_ID, qcResultId: QC_RESULT_ID, taskId: TASK_ID, layer: 'checkLayer' };
                    this.checkLayer = yq.layerShow({
                        title: getI18nValue('复检'),
                        offset: 't',// 如果去掉,就是默认居中
                        area: ['100%', '100%'],
                        type: 2,//2或者iframe为iframe方式打开弹层,1或div为div方式
                    }, '/cc-quality/pages/qualityControl/result/qc-result-media-check.html', data)
                } else if (CHANNEL_TYPE == '3') {
                    // 邮件
                    var data = { isZnCheck: 'Y', groupId: EXAM_GROUP_ID, serialId: SERIAL_ID, objId: OBJ_ID, classId: CLASS_ID, znClassId: ZN_CLASS_ID, qcResultId: QC_RESULT_ID, isRead: true, taskId: TASK_ID, layer: 'checkLayer' };
                    this.checkLayer = yq.layerShow({
                        title: getI18nValue('复检'),
                        offset: 't',// 如果去掉,就是默认居中
                        area: ['100%', '100%'],
                        type: 2,//2或者iframe为iframe方式打开弹层,1或div为div方式
                    }, '/cc-quality/pages/qualityControl/result/qc-result-email-check.html', data)
                } else if (CHANNEL_TYPE == '4') {
                    // 工单
                    var data = { isZnCheck: 'Y', groupId: EXAM_GROUP_ID, serialId: SERIAL_ID, znClassId: ZN_CLASS_ID, objId: OBJ_ID, classId: CLASS_ID, qcResultId: QC_RESULT_ID, layer: 'checkLayer', taskId: TASK_ID, type: "1" };
                    this.checkLayer = yq.layerShow({
                        title: getI18nValue('复检'),
                        offset: 't',// 如果去掉,就是默认居中
                        area: ['100%', '100%'],
                        type: 2,//2或者iframe为iframe方式打开弹层,1或div为div方式
                    }, '/cc-quality/pages/qualityControl/result/qc-result-order-check.html', data)
                } else if (CHANNEL_TYPE == '9') {
                    // 第三方
                    var data = { isZnCheck: 'Y', type: "1", templateId: TEMPLATE_ID, groupId: EXAM_GROUP_ID, serialId: SERIAL_ID, objId: OBJ_ID, classId: CLASS_ID, znClassId: ZN_CLASS_ID, qcResultId: QC_RESULT_ID, taskId: TASK_ID, layer: 'checkLayer' };
                    this.checkLayer = yq.layerShow({
                        title: getI18nValue('复检'),
                        offset: 't',// 如果去掉,就是默认居中
                        area: ['100%', '100%'],
                        type: 2,//2或者iframe为iframe方式打开弹层,1或div为div方式
                    }, '/cc-quality/pages/qualityControl/result/qc-result-thrity-check.html', data)
                }
            },
            setCauseType(cause, type) {
                if (type == '1') {
                    if (['1', '2', '3', '4', '5', '9', '10', '14'].indexOf(cause) != -1) {
                        return getI18nValue('呼入')
                    } else {
                        return getI18nValue('呼出')
                    }
                } else if (type == '2') {
                    if (['1', '3'].indexOf(cause) != -1) {
                        return getI18nValue('会话接入')
                    } else if (cause == '4') {
                        return getI18nValue('坐席重新激活')
                    } else {
                        return getI18nValue('主动发起')
                    }
                } else {
                    return getI18nValue('未知')
                }
            },
            check() {
                // 抽查
            },
            goDetail(msg) {
                // 查看详情
                // resultData(\''+row.TEMPLATE_ID+'\',\''+row.OBJ_ID+'\',\''+row.QC_RESULT_ID+'\',\''+row.SERIAL_ID+'\',\''+row.CALL_TYPE+'\',\''+row.ZN_CLASS_ID+'\',\''+row.TEMPLATE_ID+'\',\''+row.RG_CLASS_ID+'\',\''+row.EXAM_GROUP_ID+'\',\''+row.TASK_ID+'\')
                this.resultData(msg.TEMPLATE_ID, msg.OBJ_ID, msg.QC_RESULT_ID, msg.SERIAL_ID, msg.CALL_TYPE, msg.ZN_CLASS_ID, msg.EXAM_GROUP_ID, msg.CLASS_ID, msg.TASK_ID, msg.CHANNEL_TYPE)
            },
            checkReson() {
                // 抽查原因
            },
            doSearch() {
                this.show = false
                this.pageNav.loading = true
                let form = Object.fromEntries(Object.entries(this.formData).filter(([key, value]) => {
                    // 过滤掉两个时间数组
                    return key != 'date' && key != 'date2';
                }));

                if(form.znItemIds && form.znItemIds.length > 0){
                    // 使用统一的处理方法
                    form.znItemIds = this.processZnItemIds(form.znItemIds);
                }
                let data = {
                    data: {
                        ...form,
                        ...this.statQueryForm
                    },
                    pageSize: this.pageNav.size,
                    pageIndex: this.pageNav.page,
                    pageType: '2',
                }
                let that = this
                var dt = JSON.parse(JSON.stringify(data))
                yq.tableCall('/cc-quality/webcall?action=QcResultDao.znResultListByThirdParty', data, function (res) {
                    if (res.state == 1) {
                        that.tableData = res.data
                    }else{
                        yq.msg({
                            type:'error',
                            message: res.msg
                        })
                    }
                }).finally(res => {
                    this.pageNav.loading = false
                })
                dt.data.pageType = '3'
                yq.tableCall('/cc-quality/webcall?action=QcResultDao.znResultListByThirdParty', dt, function (res) {
                    if (res.state == 1) {
                        that.pageNav.total = res.totalRow
                    }
                })
            },
            handlePrevPage() {
                if (this.pageNav.page == 1) return
                this.pageNav.page = this.pageNav.page - 1
                this.doSearch()
            },
            handleNextPage() {
                this.pageNav.page = this.pageNav.page + 1
                this.doSearch()
            },
            reset() {
                this.$refs.form.resetFields();
                // 清空任务和质检规则选择
                this.formData.taskId = '';
                this.formData.znItemIds = [];
                this.znResultItemList = [];
                this.TASKBYCHANNEL = {};
            },
            handleCurrentChange(val) {
                this.pageNav.page = val
                this.doSearch()
            },
            handleSizeChange(val) {
                this.pageNav.size = val
                this.doSearch()
            },
            getDict() {
                let data = {
                    params: {},
                    controls: ["common.getDict(THIRD_PARTY_SOURCE)","common.getDict(THIRD_PARTY_BUSI_DIRECTION)","common.getDict(THIRD_PARTY_BUSI_TYPE)","QcCommonDao.getChannelTask", "QcCommonDao.getDict(QC_CHANNEL_TYPE)", "QcCommonDao.getDict(QUALITY_COACHING_RESULT)",'QcCommonDao.getTemplateId' ,'QcCommonDao.getDict(THIRD_PARTY_BUSI_TYPE)',]
                }
                let _this = this
                yq.daoCall(data, null, {
                    "contextPath": "/cc-quality"
                }).then(function (res) {//接口的方式渲染
                    _this.ChannelTask = res["QcCommonDao.getChannelTask"].data
                    _this.QC_CHANNEL_TYPE = res["QcCommonDao.getDict(QC_CHANNEL_TYPE)"].data
                    _this.QUALITY_COACHING_RESULT = res["QcCommonDao.getDict(QUALITY_COACHING_RESULT)"].data
                    _this.templateList = res['QcCommonDao.getTemplateId'].data
                    _this.busyTypeOptions = res['QcCommonDao.getDict(THIRD_PARTY_BUSI_TYPE)'].data

                    _this.THIRD_PARTY_BUSI_TYPE = res['common.getDict(THIRD_PARTY_BUSI_TYPE)'].data
                    _this.THIRD_PARTY_BUSI_DIRECTION = res['common.getDict(THIRD_PARTY_BUSI_DIRECTION)'].data
                    _this.THIRD_PARTY_SOURCE = res['common.getDict(THIRD_PARTY_SOURCE)'].data



                })
            },
        },
        mounted: function () {
            let that = this
            // yq.remoteCall("/cc_quality_Web_exploded/servlet/qcRecord?action=isAdmin", {}, function (result) {
            //     if (result.state == 1) {
            //         that.isAdmin = true
            //     }
            // });
            var currentDate = new Date();
            var year = currentDate.getFullYear();
            var month = String(currentDate.getMonth() + 1).padStart(2, '0');
            var day = String(currentDate.getDate()).padStart(2, '0');
            var formattedDate = `${year}-${month}-${day}`;
            console.log(formattedDate);
            this.formData.date = [formattedDate, formattedDate]
            this.formData.beginStartDate = formattedDate
            this.formData.endStartDate = formattedDate
            this.getDict()
            // 不在初始化时执行搜索，避免显示"请先选择质检模版!"的提示
            // this.doSearch()
        }
    })
</script>
</body>

</html>