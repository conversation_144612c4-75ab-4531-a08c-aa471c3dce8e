package com.yunqu.yc.quality.dao.sql;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.JSONArray;
import org.apache.log4j.Logger;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.SystemParamUtil;
import com.yunqu.yc.quality.base.CommonLogger;
import com.yunqu.yc.quality.base.Constants;
import com.yunqu.yc.quality.utils.PhoneCryptor;
import com.yunqu.yc.quality.utils.StringUtil;
import com.yunqu.yc.quality.cache.ThirdPartyFieldCache;
import com.yunqu.yc.quality.utils.SQLUtil;

public class QcResultSql {

	private static Logger logger = CommonLogger.logger;
	/**
	 * 质检报表-获取智能质检列表sql
	 * @return
	 */
	public static EasySQL getZnResultListSql(UserModel user, JSONObject param, EasyQuery query){
		String taskId = param.getString("task");
		String busiType = param.getString("busiType");
		String callType = param.getString("callType");
		if(CommonUtil.isNotBlank(taskId)) {
			String[] c_t = taskId.split(",");
			if(c_t != null && c_t.length > 1) {
				taskId = c_t[1];
			}
		}
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		
		String start_time = param.getString("beginServiceTime");
		String end_time = param.getString("endServiceTime");

		String keyword = param.getString("keyword"); //关键字
		String znScoreStart = param.getString("znScoreStart"); //智能质检分数开始
		String znScoreEnd = param.getString("znScoreEnd"); //智能质检分数结束
		boolean isAsrInfo = StringUtils.equals("1", param.getString("isAsrInfo"));
		String callDurationStart = param.getString("callDurationStart"); //通话时长开始
		String callDurationEnd = param.getString("callDurationEnd"); //通话时长结束
		String agentAverageVolumeStart = param.getString("agentAverageVolumeStart"); //坐席平均音量开始
		String agentAverageVolumeEnd = param.getString("agentAverageVolumeEnd"); //坐席平均音量结束
		String overallSilenceStart = param.getString("overallSilenceStart"); //整体静默开始
		String overallSilenceEnd = param.getString("overallSilenceEnd"); //整体静默结束
		String silenceRatioStart = param.getString("silenceRatioStart"); //静默比例开始
		String silenceRatioEnd = param.getString("silenceRatioEnd"); //静默比例结束
		String agentAverageSpeedStart = param.getString("agentAverageSpeedStart"); //坐席平均语速开始
		String agentAverageSpeedEnd = param.getString("agentAverageSpeedEnd"); //坐席平均语速结束
		JSONArray znItemIds = param.getJSONArray("znItemIds"); //智能质检项id

		//是否开启发布功能
		String openIssue =  SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_ISSUE");
		
		//获取限制查询事件的缓存，限制可以查看几天前的数据
		String resultConfigValue = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "RESULT_CONFIG");
		String nowTime = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
		int beforeNum = CommonUtil.parseInt(resultConfigValue, 0);
		String beforeTime = "";
		if(beforeNum != 0){
			beforeTime = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, nowTime, beforeNum * -1);
		}
		EasySQL sql = new EasySQL("SELECT t1.TASK_ID,T3.*, T2.TASK_NAME, T1.CHANNEL_TYPE,T1.SERIAL_ID AS SERIAL_ID_OBJ,T2.EXAM_GROUP_ID,T1.ID AS OBJ_ID,t2.RG_CLASS_ID ");
		sql.append(",T1.AGENT_NAME,T1.AGENT_ACC,T1.TEMPLATE_ID,T1.BUSI_TYPE");
		sql.append(" FROM " + getTableName("CC_QC_TASK_OBJ", user.getSchemaName()) + " T1 ");
		sql.append(" LEFT JOIN " + getTableName("CC_QC_TASK", user.getSchemaName()) + " T2 on T1.TASK_ID=T2.ID ");
		sql.append(" LEFT JOIN " + getTableName("CC_QC_CAPACITY", user.getSchemaName())
					+ " T3 on T3.SERIAL_ID=T1.SERIAL_ID AND T3.ZN_CLASS_ID=T2.ZN_CLASS_ID AND T1.ZN_STATE IN ('3','4') ");
		if (StringUtil.isNotBlank(keyword)){
			sql.append("LEFT JOIN "+ getTableName("cc_qc_transfer T4",user.getSchemaName())+" ON T1.SERIAL_ID = T4.SERIAL_ID ");
		}
		if (isAsrInfo){
			sql.append("LEFT JOIN "+getTableName("cc_qc_asr_info T5",user.getSchemaName())+" ON T1.SERIAL_ID = T5.SERIAL_ID ");
		}
//		if (znItemIds != null && znItemIds.size() > 0){
//			sql.append("LEFT JOIN "+getTableName("cc_qc_details T6",user.getSchemaName())+" ON T3.ID = T6.CAPACITY_ID");
//		}
		sql.append("where 1=1");
		sql.append(param.getString("channelType"), "AND T1.CHANNEL_TYPE=?");
		
		if(StringUtil.equals("Y", openIssue)) {
			//开启发布功能只查状态为4的数据
			sql.append("AND T1.ZN_STATE IN ('3','4')");
//			sql.append(" AND T1.RG_STATE IN ('1','2')");
		}else {
			sql.append("AND T1.ZN_STATE = '4'");
//			sql.append(" AND T1.RG_STATE IN ('1','2')");
		}
		
		sql.append(taskId, "AND T2.ID=? ");
		sql.append(busiType, "AND T1.BUSI_TYPE = ? ");
		sql.append(callType, "AND T1.CHANNEL_TYPE = ? ");

		// 获取数据权限
 		JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(user.getEpCode(), user.getBusiOrderId(), user.getUserAcc() + "~" + "cc-qc-znzjjg"); // ‘智能质检结果’菜单目录ID
 		StringBuilder dataAuthSql = new StringBuilder();
 		if(obj != null) {
 			String depts = obj.getString("depts");
 			dataAuthSql.append(" AND ( T1.ID='-1' ");
 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
 				dataAuthSql.append(" OR T1.AGENT_DEPT_CODE IN ('" + String.join("','", depts.split(",")) + "')");
 			}
 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
 				dataAuthSql.append(" OR T1.AGENT_ACC = '" + user.getUserAcc() + "'");
 			}
 			dataAuthSql.append(")");
 			if(StringUtils.isNotBlank(obj.getString("all"))) {
 				dataAuthSql = new StringBuilder();
 			}
 		} else {
			dataAuthSql.append(" AND T1.ID='-1' ");
		}
		sql.append(dataAuthSql.toString());
		
		String agentName = param.getString("agentName");
		if(CommonUtil.isNotBlank(agentName)) {
			sql.appendLike(agentName, "AND T1.AGENT_NAME like ?");
		}
		sql.append(user.getEpCode(), " and T1.ENT_ID = ?");
		sql.append(user.getBusiOrderId(), " and T1.BUSI_ORDER_ID = ?");
		sql.append(user.getEpCode(), " and T3.ENT_ID = ?");
		sql.append(user.getBusiOrderId(), " and T3.BUSI_ORDER_ID = ?");
		sql.appendLike(param.getString("serial")," AND T1.SERIAL_ID LIKE ? ");
		if(StringUtils.isNotBlank(startDate)){
			sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
		}
		if(StringUtils.isNotBlank(start_time)){
			sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
		}
		if(StringUtils.isNotBlank(end_time)){
			sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
		}
		if(beforeNum != 0){
			sql.append(beforeTime + " 23:59:59", " AND T3.QC_TIME <= ?");
		}
		DBTypes types = query.getTypes();
		if (DBTypes.ORACLE == types || DBTypes.PostgreSql == types|| DBTypes.DAMENG == types) {
			sql.append("AND T3.ID is not null ");
			sql.append("AND T1.AGENT_ACC is not null ");
		}else{
			sql.append("AND T3.ID <> '' ");
			sql.append("AND T1.AGENT_ACC is not null ");
			sql.append("AND T1.AGENT_ACC != '' ");
		}
		//关键字 全模糊匹配
		sql.appendLike(keyword, " and T4.CONTENT like ? ");
		//智能质检分数区间筛选
		sql.append(znScoreStart, " and T1.ZN_QC_SCORE >= ?");
		sql.append(znScoreEnd, " and T1.ZN_QC_SCORE <= ?");
		//录音属性筛选
		if (isAsrInfo){
			sql.append(callDurationStart, " and T5.CALL_DURATION >= ? ");
			sql.append(callDurationEnd, " and T5.CALL_DURATION <= ? ");
			sql.append(agentAverageVolumeStart, " and T5.AGENT_AVERAGE_VOLUME >= ?");
			sql.append(agentAverageVolumeEnd, " and T5.AGENT_AVERAGE_VOLUME <= ?");
			sql.append(overallSilenceStart, " and T5.OVERALL_SILENCE >= ?");
			sql.append(overallSilenceEnd, " and T5.OVERALL_SILENCE <= ?");
			sql.append(silenceRatioStart, " and T5.SILENCE_RATIO >= ?");
			sql.append(silenceRatioEnd, " and T5.SILENCE_RATIO <= ?");
			sql.append(agentAverageSpeedStart, " and T5.AGENT_AVERAGE_SPEED >= ?");
			sql.append(agentAverageSpeedEnd, " and T5.AGENT_AVERAGE_SPEED <= ?");
		}
		if (znItemIds != null && znItemIds.size() > 0){
			//将znItemIds转换为字符串数组
			String[] znItemIdsArray = znItemIds.stream()
					.map(Object::toString)
					.toArray(String[]::new);
			sql.append("   and  EXISTS  (SELECT 1 FROM " + getTableName("cc_qc_details T6",user.getSchemaName())+ " WHERE T3.ID = T6.CAPACITY_ID ");
			sql.appendIn(znItemIdsArray," and T6.ITEMNAME_ID ");
			sql.append(")");
		}

		String sortName = param.getString("sortName");
		String sortType = param.getString("sortType");
		if (StringUtils.isBlank(sortType) || StringUtils.isBlank(sortName)) {
			sql.append(" ORDER BY TASK_NAME desc,T3.QC_TIME desc ");
		} else {
			sql.appendSort(sortName, sortType, " TASK_NAME desc, T3.QC_TIME desc ");
		}
		logger.info("智能质检查询结果sql:"+sql.getSQL()+ ",param:"+JSON.toJSONString(sql.getParams()));
   	    return sql;
    }
	public static EasySQL getZnResultListByThirdSql(UserModel user, JSONObject param, EasyQuery query){
		String busiType = param.getString("busiType");
		String callType = param.getString("callType");
		String templateId = param.getString("templateId"); // 新增templateId参数

		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");

		String start_time = param.getString("beginServiceTime");
		String end_time = param.getString("endServiceTime");

		String keyword = param.getString("keyword"); //关键字
		String znScoreStart = param.getString("znScoreStart"); //智能质检分数开始
		String znScoreEnd = param.getString("znScoreEnd"); //智能质检分数结束
		boolean isAsrInfo = StringUtils.equals("1", param.getString("isAsrInfo"));
		String callDurationStart = param.getString("callDurationStart"); //通话时长开始
		String callDurationEnd = param.getString("callDurationEnd"); //通话时长结束
		String agentAverageVolumeStart = param.getString("agentAverageVolumeStart"); //坐席平均音量开始
		String agentAverageVolumeEnd = param.getString("agentAverageVolumeEnd"); //坐席平均音量结束
		String overallSilenceStart = param.getString("overallSilenceStart"); //整体静默开始
		String overallSilenceEnd = param.getString("overallSilenceEnd"); //整体静默结束
		String silenceRatioStart = param.getString("silenceRatioStart"); //静默比例开始
		String silenceRatioEnd = param.getString("silenceRatioEnd"); //静默比例结束
		String agentAverageSpeedStart = param.getString("agentAverageSpeedStart"); //坐席平均语速开始
		String agentAverageSpeedEnd = param.getString("agentAverageSpeedEnd"); //坐席平均语速结束
		JSONArray znItemIds = param.getJSONArray("znItemIds"); //智能质检项id

//是否开启发布功能
		String openIssue =  SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_ISSUE");

//获取限制查询事件的缓存，限制可以查看几天前的数据
		String resultConfigValue = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "RESULT_CONFIG");
		String nowTime = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
		int beforeNum = CommonUtil.parseInt(resultConfigValue, 0);
		String beforeTime = "";
		if(beforeNum != 0){
			beforeTime = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, nowTime, beforeNum * -1);
		}

		EasySQL sql = new EasySQL("SELECT T1.TASK_ID,T3.*, T2.TASK_NAME, T1.CHANNEL_TYPE,T1.SERIAL_ID AS SERIAL_ID_OBJ,T2.EXAM_GROUP_ID,T1.ID AS OBJ_ID,t2.RG_CLASS_ID ");
		sql.append(",T1.AGENT_NAME,T1.AGENT_ACC,T1.TEMPLATE_ID,T1.BUSI_TYPE");

// 添加第三方动态字段到SELECT部分
		if (StringUtils.isNotBlank(templateId)) {
			// 获取可查询字段
			ThirdPartyFieldCache thirdFieldCache = new ThirdPartyFieldCache();
			JSONObject fieldCache = thirdFieldCache.getCache(user.getEpCode(), user.getBusiOrderId(), templateId);

			// 添加动态字段到SELECT部分
			JSONArray queryForList = fieldCache.getJSONArray("showList");
			for (int i = 0; i < queryForList.size(); i++) {
				JSONObject jsonObject = queryForList.getJSONObject(i);

				if ("TEMPLATE_ID".equals(jsonObject.getString("FIELD_EN"))) {
					continue;
				}

				sql.append(",T7."+jsonObject.getString("FIELD_EN"));
				sql.append(" as THIRD_"+jsonObject.getString("FIELD_EN"));
			}
		}

		sql.append(" FROM " + getTableName("CC_QC_TASK_OBJ", user.getSchemaName()) + " T1 ");
		sql.append(" LEFT JOIN " + getTableName("CC_QC_TASK", user.getSchemaName()) + " T2 on T1.TASK_ID=T2.ID ");
		sql.append(" LEFT JOIN " + getTableName("CC_QC_CAPACITY", user.getSchemaName())
				+ " T3 on T3.SERIAL_ID=T1.SERIAL_ID AND T3.ZN_CLASS_ID=T2.ZN_CLASS_ID AND T1.ZN_STATE IN ('3','4') ");
		if (StringUtil.isNotBlank(keyword)){
			sql.append("LEFT JOIN "+ getTableName("cc_qc_transfer T4",user.getSchemaName())+" ON T1.SERIAL_ID = T4.SERIAL_ID ");
		}
		if (isAsrInfo){
			sql.append("LEFT JOIN "+getTableName("cc_qc_asr_info T5",user.getSchemaName())+" ON T1.SERIAL_ID = T5.SERIAL_ID ");
		}

// 新增：第三方表关联
		if (StringUtils.isNotBlank(templateId)) {
			sql.append("LEFT JOIN " + getTableName("CC_QC_THIRD_RECORD", user.getSchemaName()) + " T7 ON T7.SERIAL_ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='9'");
		}

		sql.append("where 1=1");
		sql.append(param.getString("channelType"), "AND T1.CHANNEL_TYPE=?");

// 新增：templateId条件
		sql.append(templateId," and T2.template_id = ?");

		if(StringUtil.equals("Y", openIssue)) {
			//开启发布功能只查状态为4的数据
			sql.append("AND T1.ZN_STATE IN ('3','4')");
		}else {
			sql.append("AND T1.ZN_STATE = '4'");
		}

		sql.append(busiType, "AND T1.BUSI_TYPE = ? ");
		sql.append(callType, "AND T1.CHANNEL_TYPE = ? ");

// 获取数据权限
		JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(user.getEpCode(), user.getBusiOrderId(), user.getUserAcc() + "~" + "cc-qc-znzjjg"); // '智能质检结果'菜单目录ID
		StringBuilder dataAuthSql = new StringBuilder();
		if(obj != null) {
			String depts = obj.getString("depts");
			dataAuthSql.append(" AND ( T1.ID='-1' ");
			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
				dataAuthSql.append(" OR T1.AGENT_DEPT_CODE IN ('" + String.join("','", depts.split(",")) + "')");
			}
			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
				dataAuthSql.append(" OR T1.AGENT_ACC = '" + user.getUserAcc() + "'");
			}
			dataAuthSql.append(")");
			if(StringUtils.isNotBlank(obj.getString("all"))) {
				dataAuthSql = new StringBuilder();
			}
		} else {
			dataAuthSql.append(" AND T1.ID='-1' ");
		}
		sql.append(dataAuthSql.toString());

		String agentName = param.getString("agentName");
		if(CommonUtil.isNotBlank(agentName)) {
			sql.appendLike(agentName, "AND T1.AGENT_NAME like ?");
		}
		sql.append(user.getEpCode(), " and T1.ENT_ID = ?");
		sql.append(user.getBusiOrderId(), " and T1.BUSI_ORDER_ID = ?");
		sql.append(user.getEpCode(), " and T3.ENT_ID = ?");
		sql.append(user.getBusiOrderId(), " and T3.BUSI_ORDER_ID = ?");
		sql.appendLike(param.getString("serial")," AND T1.SERIAL_ID LIKE ? ");
		if(StringUtils.isNotBlank(startDate)){
			sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
		}
		if(StringUtils.isNotBlank(start_time)){
			sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
		}
		if(StringUtils.isNotBlank(end_time)){
			sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
		}
		if(beforeNum != 0){
			sql.append(beforeTime + " 23:59:59", " AND T3.QC_TIME <= ?");
		}
		DBTypes types = query.getTypes();
		if (DBTypes.ORACLE == types || DBTypes.PostgreSql == types|| DBTypes.DAMENG == types) {
			sql.append("AND T3.ID is not null ");
			sql.append("AND T1.AGENT_ACC is not null ");
		}else{
			sql.append("AND T3.ID <> '' ");
			sql.append("AND T1.AGENT_ACC is not null ");
			sql.append("AND T1.AGENT_ACC != '' ");
		}
//关键字 全模糊匹配
		sql.appendLike(keyword, " and T4.CONTENT like ? ");
//智能质检分数区间筛选
		sql.append(znScoreStart, " and T1.ZN_QC_SCORE >= ?");
		sql.append(znScoreEnd, " and T1.ZN_QC_SCORE <= ?");
//录音属性筛选
		if (isAsrInfo){
			sql.append(callDurationStart, " and T5.CALL_DURATION >= ? ");
			sql.append(callDurationEnd, " and T5.CALL_DURATION <= ? ");
			sql.append(agentAverageVolumeStart, " and T5.AGENT_AVERAGE_VOLUME >= ?");
			sql.append(agentAverageVolumeEnd, " and T5.AGENT_AVERAGE_VOLUME <= ?");
			sql.append(overallSilenceStart, " and T5.OVERALL_SILENCE >= ?");
			sql.append(overallSilenceEnd, " and T5.OVERALL_SILENCE <= ?");
			sql.append(silenceRatioStart, " and T5.SILENCE_RATIO >= ?");
			sql.append(silenceRatioEnd, " and T5.SILENCE_RATIO <= ?");
			sql.append(agentAverageSpeedStart, " and T5.AGENT_AVERAGE_SPEED >= ?");
			sql.append(agentAverageSpeedEnd, " and T5.AGENT_AVERAGE_SPEED <= ?");
		}
		if (znItemIds != null && znItemIds.size() > 0){
			//将znItemIds转换为字符串数组
			String[] znItemIdsArray = znItemIds.stream()
					.map(Object::toString)
					.toArray(String[]::new);
			sql.append("   and  EXISTS  (SELECT 1 FROM " + getTableName("cc_qc_details T6",user.getSchemaName())+ " WHERE T3.ID = T6.CAPACITY_ID ");
			sql.appendIn(znItemIdsArray," and T6.ITEMNAME_ID ");
			sql.append(")");
		}

// 新增：添加第三方动态搜索条件
		if (StringUtils.isNotBlank(templateId)) {
			ThirdPartyFieldCache thirdFieldCache = new ThirdPartyFieldCache();
			JSONObject fieldCache = thirdFieldCache.getCache(user.getEpCode(), user.getBusiOrderId(), templateId);
			JSONArray searchForList = fieldCache.getJSONArray("searchList");

			// 使用SQLUtil.extendQuery2方法添加动态搜索条件
			SQLUtil.extendQuery2("T7.", param.getJSONObject("form"), searchForList, sql);
		}

		String sortName = param.getString("sortName");
		String sortType = param.getString("sortType");
		if (StringUtils.isBlank(sortType) || StringUtils.isBlank(sortName)) {
			sql.append(" ORDER BY TASK_NAME desc,T3.QC_TIME desc ");
		} else {
			sql.appendSort(sortName, sortType, " TASK_NAME desc, T3.QC_TIME desc ");
		}
		logger.info("智能质检查询结果sql:"+sql.getFullSql());
		return sql;
	}

	/**
	 * 智能质检离线导出SQL
	 */
	public static EasySQL getZnResultListSqlForOfflineExport(UserModel user, JSONObject param, HttpServletRequest request, EasyQuery query){
		//是否开启发布功能
		String openIssue =  SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_ISSUE");
		//质检时间
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		//服务时间
		String start_time = param.getString("beginServiceTime");
		String end_time = param.getString("endServiceTime");
		String task = param.getString("task");
		if(CommonUtil.isNotBlank(task)) {
			String[] c_t = task.split(",");
			if(c_t != null && c_t.length > 1) {
				task = c_t[1];
			}
		}

		EasySQL sql = new EasySQL("select TT.*,'' AS ZN_EVALUATE_RESULT from (");
		//语音
		sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
		sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
		sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.SERIAL_ID AS CHANNEL_SERIAL_ID");
		sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_CALL_RECORD") + " T4 ON T4.SERIAL_ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='1'");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
		sql.append("where 1=1");
		sql.append("and T1.CHANNEL_TYPE='1'");
		sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
		sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		if(StringUtil.equals("Y", openIssue)) {
			//开启发布功能只查状态为4的数据
			sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
		}else {
			sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
		}
		//时间筛选
		if(StringUtils.isNotBlank(startDate)){
			sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
		}
		if(StringUtils.isNotBlank(start_time)){
			sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
		}
		if(StringUtils.isNotBlank(end_time)){
			sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
		}
		//质检任务ID
		sql.append(task," AND T1.TASK_ID = ? ");

		sql.append(" union all " );

		//全媒体
		sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
		sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
		sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CUST_CODE AS CALLER,T4.SERIAL_ID AS CHANNEL_SERIAL_ID");
		sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_MEDIA_RECORD") + " T4 ON T4.SERIAL_ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='2'");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
		sql.append("where 1=1");
		sql.append("and T1.CHANNEL_TYPE='2'");
		sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
		sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		if(StringUtil.equals("Y", openIssue)) {
			//开启发布功能只查状态为4的数据
			sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
		}else {
			sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
		}
		//时间筛选
		if(StringUtils.isNotBlank(startDate)){
			sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
		}
		if(StringUtils.isNotBlank(start_time)){
			sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
		}
		if(StringUtils.isNotBlank(end_time)){
			sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
		}
		//质检任务ID
		sql.append(task," AND T1.TASK_ID = ? ");

		//邮件
		String emailOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_EMAIL_QC");
		logger.info("智能质检离线导出-是否开启邮件质检:"+emailOpen);
		if("Y".equals(emailOpen)) {
			sql.append(" union all " );

			sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
			sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
			sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.EMAIL_FROM AS CALLER,T4.ID AS CHANNEL_SERIAL_ID");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_EMAIL_SESSION") + " T4 ON T4.ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='3'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");
			sql.append("and T1.CHANNEL_TYPE='3'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
			}else {
				sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
			}
			//时间筛选
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
			}
			if(StringUtils.isNotBlank(start_time)){
				sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
			}
			//质检任务ID
			sql.append(task," AND T1.TASK_ID = ? ");
		}

		//第三方
		String thirdOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OEPN_THIRD_PARTY_QC");
		logger.info("智能质检离线导出-是否开启第三方质检:"+thirdOpen);
		if("Y".equals(thirdOpen)) {
			sql.append(" union all " );

			sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
			sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
			sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.OBJECT_ID AS CHANNEL_SERIAL_ID");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_THIRD_RECORD") + " T4 ON T4.SERIAL_ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='9'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");
			sql.append("and T1.CHANNEL_TYPE='9'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
			}else {
				sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
			}
			//时间筛选
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
			}
			if(StringUtils.isNotBlank(start_time)){
				sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
			}
			//质检任务ID
			sql.append(task," AND T1.TASK_ID = ? ");
		}

		//工单
		String orderOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OEPN_ORDER_QC");
		logger.info("智能质检离线导出-是否开启工单质检:"+orderOpen);
		if("Y".equals(orderOpen)) {
			sql.append(" union all " );

			sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
			sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
			sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.ORDER_NO AS CHANNEL_SERIAL_ID");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_BO_BASE_ORDER") + " T4 ON T4.ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='4'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");
			sql.append("and T1.CHANNEL_TYPE='4'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			//防止工单重复
			sql.append(user.getEpCode(), " and 	T4.EP_CODE =? ");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
			}else {
				sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
			}
			//时间筛选
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
			}
			if(StringUtils.isNotBlank(start_time)){
				sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
			}
			//质检任务ID
			sql.append(task," AND T1.TASK_ID = ? ");

			sql.append(" union all " );

			//工单老化表
			sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
			sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
			sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.ORDER_NO AS CHANNEL_SERIAL_ID");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_BO_BASE_ORDER_HIS") + " T4 ON T4.ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='4'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");
			sql.append("and T1.CHANNEL_TYPE='4'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			//防止工单重复
			sql.append(user.getEpCode(), " and 	T4.EP_CODE =? ");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
			}else {
				sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
			}
			//时间筛选
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
			}
			if(StringUtils.isNotBlank(start_time)){
				sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
			}
			//质检任务ID
			sql.append(task," AND T1.TASK_ID = ? ");
		}
		sql.append(") TT ");
		sql.append(" ORDER BY TT.SERVICE_TIME desc,TT.DATE_ID desc ");
		logger.info("智能质检查询结果sql:"+sql.getSQL()+ ",param:"+JSON.toJSONString(sql.getParams()));
		return sql;
	}

	/**
	 * 智能质检离线导出SQL(优化)
	 */
	public static EasySQL getZnResultListSqlForOfflineExport2(UserModel user, JSONObject param, HttpServletRequest request, EasyQuery query){
		//是否开启发布功能
		String openIssue =  SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_ISSUE");
		//质检时间
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		//获取数据库类型
		DBTypes types = query.getTypes();
		//服务时间
		String start_time = param.getString("beginServiceTime");
		String end_time = param.getString("endServiceTime");
		String task = param.getString("task");
		if(CommonUtil.isNotBlank(task)) {
			String[] c_t = task.split(",");
			if(c_t != null && c_t.length > 1) {
				task = c_t[1];
			}
		}

		EasySQL sql = new EasySQL("select TT.* from (");
		//语音
		sql.append("SELECT tkl.*,pkl.ZN_EVALUATE_RESULT FROM (");

		sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
		sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
		sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.SERIAL_ID AS CHANNEL_SERIAL_ID");
		sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_CALL_RECORD") + " T4 ON T4.SERIAL_ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='1'");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
//		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS") + " T6 ON T3.ID = T6.CAPACITY_ID");
		sql.append("where 1=1");
		sql.append("and T1.CHANNEL_TYPE='1'");
		sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
		sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		if(StringUtil.equals("Y", openIssue)) {
			//开启发布功能只查状态为4的数据
			sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
		}else {
			sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
		}
		//时间筛选
		if(StringUtils.isNotBlank(startDate)){
			sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
		}
		if(StringUtils.isNotBlank(start_time)){
			sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
		}
		if(StringUtils.isNotBlank(end_time)){
			sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
		}
		//质检任务ID
		sql.append(task," AND T1.TASK_ID = ? ");

		sql.append(") tkl ");
		sql.append("LEFT JOIN (SELECT CAPACITY_ID, ");
		if( DBTypes.ORACLE == types || DBTypes.DAMENG == types) {
			sql.append("LISTAGG(ITEM_NAME,';') AS ZN_EVALUATE_RESULT ");
		} else if (DBTypes.PostgreSql == types) {
			sql.append("STRING_AGG(ITEM_NAME,';') AS ZN_EVALUATE_RESULT ");
		} else {
			sql.append("GROUP_CONCAT(ITEM_NAME SEPARATOR ';') AS ZN_EVALUATE_RESULT ");
		}
		sql.append("FROM " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS x ") + "WHERE SCORE < 0 GROUP BY CAPACITY_ID) pkl ");
		sql.append("ON tkl.CAPACITY_ID = pkl.CAPACITY_ID");

//		sql.append("GROUP BY T3.ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME,T1.ZN_QC_SCORE,T1.ZN_PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG,T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.SERIAL_ID");

		sql.append(" union all " );

		//全媒体
		sql.append("SELECT tkl.*,pkl.ZN_EVALUATE_RESULT FROM (");

		sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
		sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
		sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CUST_CODE AS CALLER,T4.SERIAL_ID AS CHANNEL_SERIAL_ID");
		sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_MEDIA_RECORD") + " T4 ON T4.SERIAL_ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='2'");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
//		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS") + " T6 ON T3.ID = T6.CAPACITY_ID");
		sql.append("where 1=1");
		sql.append("and T1.CHANNEL_TYPE='2'");
		sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
		sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		if(StringUtil.equals("Y", openIssue)) {
			//开启发布功能只查状态为4的数据
			sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
		}else {
			sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
		}
		//时间筛选
		if(StringUtils.isNotBlank(startDate)){
			sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
		}
		if(StringUtils.isNotBlank(start_time)){
			sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
		}
		if(StringUtils.isNotBlank(end_time)){
			sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
		}
		//质检任务ID
		sql.append(task," AND T1.TASK_ID = ? ");

		sql.append(") tkl ");
		sql.append("LEFT JOIN (SELECT CAPACITY_ID, ");
		if(DBTypes.ORACLE == types || DBTypes.DAMENG == types) {
			sql.append("LISTAGG(ITEM_NAME,';') AS ZN_EVALUATE_RESULT ");
		}else if (DBTypes.PostgreSql == types) {
			sql.append("STRING_AGG(ITEM_NAME,';') AS ZN_EVALUATE_RESULT ");
		} else {
			sql.append("GROUP_CONCAT(ITEM_NAME SEPARATOR ';') AS ZN_EVALUATE_RESULT ");
		}
		sql.append("FROM " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS x ") + "WHERE SCORE < 0 GROUP BY CAPACITY_ID) pkl ");
		sql.append("ON tkl.CAPACITY_ID = pkl.CAPACITY_ID");
//		sql.append("GROUP BY T3.ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME,T1.ZN_QC_SCORE,T1.ZN_PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG,T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CUST_CODE,T4.SERIAL_ID");


		//邮件
		String emailOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_EMAIL_QC");
		logger.info("智能质检离线导出-是否开启邮件质检:"+emailOpen);
		if("Y".equals(emailOpen)) {
			sql.append(" union all " );

			sql.append("SELECT tkl.*,pkl.ZN_EVALUATE_RESULT FROM (");

			sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
			sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
			sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.EMAIL_FROM AS CALLER,T4.ID AS CHANNEL_SERIAL_ID");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_EMAIL_SESSION") + " T4 ON T4.ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='3'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
//			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS") + " T6 ON T3.ID = T6.CAPACITY_ID");
			sql.append("where 1=1");
			sql.append("and T1.CHANNEL_TYPE='3'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
			}else {
				sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
			}
			//时间筛选
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
			}
			if(StringUtils.isNotBlank(start_time)){
				sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
			}
			//质检任务ID
			sql.append(task," AND T1.TASK_ID = ? ");

			sql.append(") tkl ");
			sql.append("LEFT JOIN (SELECT CAPACITY_ID, ");
			if(DBTypes.ORACLE == types || DBTypes.DAMENG == types) {
				sql.append("LISTAGG(ITEM_NAME,';') AS ZN_EVALUATE_RESULT ");
			} else if (DBTypes.PostgreSql == types) {
				sql.append("STRING_AGG(ITEM_NAME,';') AS ZN_EVALUATE_RESULT ");
			}
			else {
				sql.append("GROUP_CONCAT(ITEM_NAME SEPARATOR ';') AS ZN_EVALUATE_RESULT ");
			}
			sql.append("FROM " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS x ") + "WHERE SCORE < 0 GROUP BY CAPACITY_ID) pkl ");
			sql.append("ON tkl.CAPACITY_ID = pkl.CAPACITY_ID");

//			sql.append("GROUP BY T3.ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME,T1.ZN_QC_SCORE,T1.ZN_PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG,T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.EMAIL_FROM,T4.ID");

		}

		//第三方
		String thirdOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OEPN_THIRD_PARTY_QC");
		logger.info("智能质检离线导出-是否开启第三方质检:"+thirdOpen);
		if("Y".equals(thirdOpen)) {
			sql.append(" union all " );

			sql.append("SELECT tkl.*,pkl.ZN_EVALUATE_RESULT FROM (");

			sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
			sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
			sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.OBJECT_ID AS CHANNEL_SERIAL_ID");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_THIRD_RECORD") + " T4 ON T4.SERIAL_ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='9'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
//			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS") + " T6 ON T3.ID = T6.CAPACITY_ID");
			sql.append("where 1=1");
			sql.append("and T1.CHANNEL_TYPE='9'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
			}else {
				sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
			}
			//时间筛选
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
			}
			if(StringUtils.isNotBlank(start_time)){
				sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
			}
			//质检任务ID
			sql.append(task," AND T1.TASK_ID = ? ");

			sql.append(") tkl ");
			sql.append("LEFT JOIN (SELECT CAPACITY_ID, ");
			if(DBTypes.ORACLE == types || DBTypes.DAMENG == types) {
				sql.append("LISTAGG(ITEM_NAME,';') AS ZN_EVALUATE_RESULT ");
			} else if (DBTypes.PostgreSql == types) {
				sql.append("STRING_AGG(ITEM_NAME,';') AS ZN_EVALUATE_RESULT ");
			} else {
				sql.append("GROUP_CONCAT(ITEM_NAME SEPARATOR ';') AS ZN_EVALUATE_RESULT ");
			}
			sql.append("FROM " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS x ") + "WHERE SCORE < 0 GROUP BY CAPACITY_ID) pkl ");
			sql.append("ON tkl.CAPACITY_ID = pkl.CAPACITY_ID");
//			sql.append("GROUP BY T3.ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME,T1.ZN_QC_SCORE,T1.ZN_PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG,T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.OBJECT_ID");
		}

		//工单
		String orderOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OEPN_ORDER_QC");
		logger.info("智能质检离线导出-是否开启工单质检:"+orderOpen);
		if("Y".equals(orderOpen)) {
			sql.append(" union all " );

			sql.append("SELECT tkl.*,pkl.ZN_EVALUATE_RESULT FROM (");

			sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
			sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
			sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.ORDER_NO AS CHANNEL_SERIAL_ID");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_BO_BASE_ORDER") + " T4 ON T4.ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='4'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
//			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS") + " T6 ON T3.ID = T6.CAPACITY_ID");
			sql.append("where 1=1");
			sql.append("and T1.CHANNEL_TYPE='4'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			//防止工单重复
			sql.append(user.getEpCode(), " and 	T4.EP_CODE =? ");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
			}else {
				sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
			}
			//时间筛选
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
			}
			if(StringUtils.isNotBlank(start_time)){
				sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
			}
			//质检任务ID
			sql.append(task," AND T1.TASK_ID = ? ");

			sql.append(") tkl ");
			sql.append("LEFT JOIN (SELECT CAPACITY_ID, ");
			if(DBTypes.ORACLE == types || DBTypes.DAMENG == types) {
				sql.append("LISTAGG(ITEM_NAME,';') AS ZN_EVALUATE_RESULT ");
			} else if (DBTypes.PostgreSql == types) {
				sql.append("STRING_AGG(ITEM_NAME,';') AS ZN_EVALUATE_RESULT ");
			} else {
				sql.append("GROUP_CONCAT(ITEM_NAME SEPARATOR ';') AS ZN_EVALUATE_RESULT ");
			}
			sql.append("FROM " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS x ") + "WHERE SCORE < 0 GROUP BY CAPACITY_ID) pkl ");
			sql.append("ON tkl.CAPACITY_ID = pkl.CAPACITY_ID");

//			sql.append("GROUP BY T3.ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME,T1.ZN_QC_SCORE,T1.ZN_PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG,T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.ORDER_NO");

			sql.append(" union all " );

			//工单老化表
			sql.append("SELECT tkl.*,pkl.ZN_EVALUATE_RESULT FROM (");

			sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
			sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
			sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.ORDER_NO AS CHANNEL_SERIAL_ID");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_BO_BASE_ORDER_HIS") + " T4 ON T4.ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='4'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
//			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS") + " T6 ON T3.ID = T6.CAPACITY_ID");
			sql.append("where 1=1");
			sql.append("and T1.CHANNEL_TYPE='4'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			//防止工单重复
			sql.append(user.getEpCode(), " and 	T4.EP_CODE =? ");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
			}else {
				sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
			}
			//时间筛选
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
			}
			if(StringUtils.isNotBlank(start_time)){
				sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
			}
			//质检任务ID
			sql.append(task," AND T1.TASK_ID = ? ");

			sql.append(") tkl ");
			sql.append("LEFT JOIN (SELECT CAPACITY_ID, ");
			if(DBTypes.ORACLE == types || DBTypes.DAMENG == types) {
				sql.append("LISTAGG(ITEM_NAME,';') AS ZN_EVALUATE_RESULT ");
			} else if (DBTypes.PostgreSql == types) {
				sql.append("STRING_AGG(ITEM_NAME,';') AS ZN_EVALUATE_RESULT ");
			} else {
				sql.append("GROUP_CONCAT(ITEM_NAME SEPARATOR ';') AS ZN_EVALUATE_RESULT ");
			}
			sql.append("FROM " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS x ") + "WHERE SCORE < 0 GROUP BY CAPACITY_ID) pkl ");
			sql.append("ON tkl.CAPACITY_ID = pkl.CAPACITY_ID");

//			sql.append("GROUP BY T3.ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME,T1.ZN_QC_SCORE,T1.ZN_PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG,T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.ORDER_NO");
		}
		sql.append(") TT ");
		sql.append(" ORDER BY TT.SERVICE_TIME desc,TT.DATE_ID desc ");
		logger.info("智能质检查询结果sql:"+sql.getSQL()+ ",param:"+JSON.toJSONString(sql.getParams()));
		return sql;
	}

	/**
	 * 智能质检离线导出SQL(质检项优化)
	 */
	public static EasySQL getZnResultListSqlForOfflineExport3(UserModel user, JSONObject param, HttpServletRequest request, EasyQuery query){
		//是否开启发布功能
		String openIssue =  SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_ISSUE");
		//质检时间
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		//获取数据库类型
		DBTypes types = query.getTypes();
		//服务时间
		String start_time = param.getString("beginServiceTime");
		String end_time = param.getString("endServiceTime");
		String task = param.getString("task");
		String channelType = "";
		if(CommonUtil.isNotBlank(task)) {
			String[] c_t = task.split(",");
			if(c_t != null && c_t.length > 1) {
				task = c_t[1];
				channelType = c_t[0];
			}
		}
		EasySQL sql = new EasySQL("");
		if (StringUtils.isBlank(channelType) || StringUtils.equals("1",channelType)){
			//语音
			sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
			sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
			sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.SERIAL_ID AS CHANNEL_SERIAL_ID,T3.ZN_EVALUATE_RESULT");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_CALL_RECORD") + " T4 ON T4.SERIAL_ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='1'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");
			sql.append("and T1.CHANNEL_TYPE='1'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
			}else {
				sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
			}
			//时间筛选
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
			}
			if(StringUtils.isNotBlank(start_time)){
				sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
			}
			//质检任务ID
			sql.append(task," AND T1.TASK_ID = ? ");
		}

		if (StringUtils.isBlank(channelType) || StringUtils.equals("2",channelType)){

			if (StringUtils.isNotBlank(sql.getSQL())){
				sql.append(" union all " );
			}

			//全媒体
			sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
			sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
			sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CUST_CODE AS CALLER,T4.SERIAL_ID AS CHANNEL_SERIAL_ID,T3.ZN_EVALUATE_RESULT");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_MEDIA_RECORD") + " T4 ON T4.SERIAL_ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='2'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");
			sql.append("and T1.CHANNEL_TYPE='2'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
			}else {
				sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
			}
			//时间筛选
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
			}
			if(StringUtils.isNotBlank(start_time)){
				sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
			}
			//质检任务ID
			sql.append(task," AND T1.TASK_ID = ? ");

		}

		if (StringUtils.isBlank(channelType) || StringUtils.equals("3",channelType)){
			//邮件
			String emailOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_EMAIL_QC");
			logger.info("智能质检离线导出-是否开启邮件质检:"+emailOpen);
			if("Y".equals(emailOpen)) {

				if (StringUtils.isNotBlank(sql.getSQL())){
					sql.append(" union all " );
				}
				sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
				sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
				sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.EMAIL_FROM AS CALLER,T4.ID AS CHANNEL_SERIAL_ID,T3.ZN_EVALUATE_RESULT");
				sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_EMAIL_SESSION") + " T4 ON T4.ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='3'");
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
				sql.append("where 1=1");
				sql.append("and T1.CHANNEL_TYPE='3'");
				sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
				sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
				if(StringUtil.equals("Y", openIssue)) {
					//开启发布功能只查状态为4的数据
					sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
				}else {
					sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
				}
				//时间筛选
				if(StringUtils.isNotBlank(startDate)){
					sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
				}
				if(StringUtils.isNotBlank(endDate)){
					sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
				}
				if(StringUtils.isNotBlank(start_time)){
					sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
				}
				if(StringUtils.isNotBlank(end_time)){
					sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
				}
				//质检任务ID
				sql.append(task," AND T1.TASK_ID = ? ");
			}
		}

		if (StringUtils.isBlank(channelType) || StringUtils.equals("9",channelType)){
			//第三方
			String thirdOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OEPN_THIRD_PARTY_QC");
			logger.info("智能质检离线导出-是否开启第三方质检:"+thirdOpen);
			if("Y".equals(thirdOpen)) {
				if (StringUtils.isNotBlank(sql.getSQL())){
					sql.append(" union all " );
				}
				sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
				sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
				sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.OBJECT_ID AS CHANNEL_SERIAL_ID,T3.ZN_EVALUATE_RESULT");
				sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_THIRD_RECORD") + " T4 ON T4.ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='9'");
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
				sql.append("where 1=1");
				sql.append("and T1.CHANNEL_TYPE='9'");
				sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
				sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
				if(StringUtil.equals("Y", openIssue)) {
					//开启发布功能只查状态为4的数据
					sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
				}else {
					sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
				}
				//时间筛选
				if(StringUtils.isNotBlank(startDate)){
					sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
				}
				if(StringUtils.isNotBlank(endDate)){
					sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
				}
				if(StringUtils.isNotBlank(start_time)){
					sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
				}
				if(StringUtils.isNotBlank(end_time)){
					sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
				}
				//质检任务ID
				sql.append(task," AND T1.TASK_ID = ? ");
			}
		}

		if (StringUtils.isBlank(channelType) || StringUtils.equals("4",channelType)){
			//工单
			String orderOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OEPN_ORDER_QC");
			logger.info("智能质检离线导出-是否开启工单质检:"+orderOpen);
			if("Y".equals(orderOpen)) {
				if (StringUtils.isNotBlank(sql.getSQL())){
					sql.append(" union all " );
				}

				sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
				sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
				sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.ORDER_NO AS CHANNEL_SERIAL_ID,T3.ZN_EVALUATE_RESULT");
				sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_BO_BASE_ORDER") + " T4 ON T4.ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='4'");
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
//			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS") + " T6 ON T3.ID = T6.CAPACITY_ID");
				sql.append("where 1=1");
				sql.append("and T1.CHANNEL_TYPE='4'");
				sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
				sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
				//防止工单重复
				sql.append(user.getEpCode(), " and 	T4.EP_CODE =? ");
				if(StringUtil.equals("Y", openIssue)) {
					//开启发布功能只查状态为4的数据
					sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
				}else {
					sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
				}
				//时间筛选
				if(StringUtils.isNotBlank(startDate)){
					sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
				}
				if(StringUtils.isNotBlank(endDate)){
					sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
				}
				if(StringUtils.isNotBlank(start_time)){
					sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
				}
				if(StringUtils.isNotBlank(end_time)){
					sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
				}
				//质检任务ID
				sql.append(task," AND T1.TASK_ID = ? ");


				if (StringUtils.isNotBlank(sql.getSQL())){
					sql.append(" union all " );
				}

				//工单老化表
				sql.append("select T3.ID AS CAPACITY_ID,T2.TASK_NAME,T1.INSPECTOR_NAME,T1.AGENT_NAME,T5.SKILL_GROUP_NAME DEPT_NAME,");
				sql.append("T1.ZN_QC_SCORE ZN_SCORE,T1.ZN_PASS_FLAG PASS_FLAG,T1.CHANNEL_TYPE,T1.ZN_RECONSIDER_FLAG APPROVAL_STATE,");
				sql.append("T1.ZN_RESULT,T3.QC_TIME,T1.SERVICE_TIME,T1.OBJ_TYPE,T1.DATE_ID,T4.CALLER,T4.ORDER_NO AS CHANNEL_SERIAL_ID,T3.ZN_EVALUATE_RESULT");
				sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ T1"));
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK T2") + " ON T1.TASK_ID = T2.ID");
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY T3") + " ON T3.SERIAL_ID = T1.SERIAL_ID");
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_BO_BASE_ORDER_HIS") + " T4 ON T4.ID = T1.SERIAL_ID AND T1.CHANNEL_TYPE='4'");
				sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T1.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T1.ENT_ID=T5.ENT_ID");
//			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_DETAILS") + " T6 ON T3.ID = T6.CAPACITY_ID");
				sql.append("where 1=1");
				sql.append("and T1.CHANNEL_TYPE='4'");
				sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
				sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
				//防止工单重复
				sql.append(user.getEpCode(), " and 	T4.EP_CODE =? ");
				if(StringUtil.equals("Y", openIssue)) {
					//开启发布功能只查状态为4的数据
					sql.append(" and T1.ZN_STATE = '4' and T1.RG_STATE in ('1','2')");
				}else {
					sql.append(" and T1.ZN_STATE in ('3', '4') and T1.RG_STATE in ('1','2')");
				}
				//时间筛选
				if(StringUtils.isNotBlank(startDate)){
					sql.append(startDate+" 00:00:00", " AND T3.QC_TIME >= ?");
				}
				if(StringUtils.isNotBlank(endDate)){
					sql.append(endDate+" 23:59:59", " AND T3.QC_TIME <= ?");
				}
				if(StringUtils.isNotBlank(start_time)){
					sql.append(start_time+" 00:00:00", " AND T1.SERVICE_TIME >= ?");
				}
				if(StringUtils.isNotBlank(end_time)){
					sql.append(end_time+" 23:59:59", " AND T1.SERVICE_TIME <= ?");
				}
				//质检任务ID
				sql.append(task," AND T1.TASK_ID = ? ");
			}
		}

		EasySQL easySQL = new EasySQL();
		easySQL.append("select TT.* from (");
		addExecuteSql(easySQL, sql);
		easySQL.append(") TT ");
		easySQL.append(" ORDER BY TT.SERVICE_TIME desc");
		logger.info("智能质检查询结果sql:"+easySQL.getSQL()+ ",param:"+JSON.toJSONString(easySQL.getParams()));
		return easySQL;
	}


	//获取人工质检结果sql (标准渠道)
	public static EasySQL getRgResultListSql(UserModel user, JSONObject param, HttpServletRequest request, EasyQuery query) {
		//是否开启发布功能
		String openIssue =  SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_ISSUE");
		EasySQL sql = new EasySQL("select TT.*,TT2.ID AS SELECT_ID,'' AS RG_EVALUATE_RESULT from (");
		//语音
		sql.append("select T5.SKILL_GROUP_NAME DEPT_NAME,t3.TEMPLATE_ID,t3.RG_CLASS_ID ,t2.SERVICE_TIME,T4.CREATE_CAUSE CREATE_CAUSE1,T4.CREATE_CAUSE CREATE_CAUSE2,t1.ONE_VOTE_ITEM");
		sql.append(",t3.ZN_CLASS_ID,t1.QC_RESULT_ID,t1.DATE_ID,t1.INSPECTOR_ID,t1.INSPECTOR_NAME,t1.QC_TIME,t1.EXAM_GROUP_ID,t1.AGENT_ID,t1.AGENT_NAME,t1.CALL_TYPE,T1.EVALUATE,T1.EVALUATE2,T1.EVALUATE3   ");
		sql.append(",t1.TASK_ID,t1.TASK_NAME,t1.PASS_SCORE ,t1.SCORE,CASE WHEN t1.SCORE IS NOT NULL THEN t1.SCORE ELSE t2.ZN_QC_SCORE END AS ACTUAL_SCORE,t1.PASS_FLAG,t1.RESULT_DESC,t1.APPROVAL_STATE,t1.RESULT_CONTENT,t1.BASE_SCORE,t2.SERIAL_ID,t2.ID AS OBJ_ID,t2.ZN_QC_SCORE ZN_SCORE");
		sql.append(",t1.CACH_RESULT,t1.CACH_EXJOSN,t1.EVALUATE_EXPLAIN,t2.OBJ_TYPE,T4.CALLER,T4.SERIAL_ID AS CHANNEL_SERIAL_ID,t2.BUSI_TYPE");
		sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT t1"));
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ t2") + " ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK t3") + " ON T3.ID = t2.TASK_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_CALL_RECORD") + " T4 ON T4.SERIAL_ID = t2.SERIAL_ID AND t2.CHANNEL_TYPE='1'");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T2.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T2.ENT_ID=T5.ENT_ID");
		sql.append("where 1=1");
		//评定等级
		sql.append(param.getString("evaluate"), " and t1.EVALUATE = ?");
		sql.append(param.getString("evaluate2"), " and t1.EVALUATE2 = ?");
		sql.append(param.getString("evaluate3"), " and t1.EVALUATE3 = ?");
		
		sql.append("and t2.CHANNEL_TYPE='1'");
		sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
		sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		if(StringUtil.equals("Y", openIssue)) {
			//开启发布功能只查状态为4的数据
			sql.append(" and t2.RG_STATE = '4'");
		}else {
			sql.append(" and t2.RG_STATE in ('3', '4') ");
		}
		//名单类型 0未分类 1优秀案例 2警示案例
		sql.append(param.getString("objType"), "and t2.OBJ_TYPE = ?");
		
		String userId = user.getUserId();
		sql.append(param.getInteger("passFlag"), " and t1.PASS_FLAG = ?");
		sql.append(param.getInteger("callType"), " and t1.CALL_TYPE = ?");
		sql.append(param.getString("approvalState"), "and t1.APPROVAL_STATE = ?");
		sql.append(param.getString("cachResult"), "and t1.CACH_RESULT = ?");
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		if(StringUtils.isNotBlank(startDate)){
			sql.append(startDate + " 00:00:00", " and t1.QC_TIME >= ?" );
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(endDate + " 23:59:59", " and t1.QC_TIME <= ?");
		}
		/*if(beforeNum != 0){
			sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), " and t1.DATE_ID <= ?");
		}*/
		String begin_time = param.getString("beginServiceTime");
		String end_time = param.getString("endServiceTime");
		if(StringUtils.isNotBlank(begin_time)){
			sql.append(begin_time+" 00:00:00","and t2.SERVICE_TIME >=?");
		}
		if(StringUtils.isNotBlank(end_time)){
			sql.append(end_time+" 23:59:59", " and t2.SERVICE_TIME <=?");
		}
		String task = param.getString("task");
		if(CommonUtil.isNotBlank(task)) {
			String[] c_t = task.split(",");
			if(c_t != null && c_t.length > 1) {
				task = c_t[1];
			}
		}
		sql.append(task," AND t1.TASK_ID = ? ");
		String isExcellent = param.getString("isExcellent");
		String excellentGradeCode = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "EXCELLENT_GRADE_CODE");
		if(StringUtil.equals("1", isExcellent)) {
			//优秀录音
			sql.append(excellentGradeCode," AND t1.EVALUATE = ? ");
		}
		sql.appendLike(param.getString("agentName"), " and t1.AGENT_NAME like ?");
		sql.append(param.getString("examGroupId"), " and t1.EXAM_GROUP_ID = ?");
		sql.append(param.getString("taskId"), " and t1.TASK_ID = ?");
		sql.append(param.getString("busiType")," and t2.BUSI_TYPE = ?");
	    String oneVoteItem = param.getString("oneVoteItem");
        if(StringUtils.isNotBlank(oneVoteItem)){
        	if("Y".equals(oneVoteItem)){
        		sql.append(" and t1.ONE_VOTE_ITEM >0"); 
        	}else if("N".equals(oneVoteItem)){
        		sql.append(" and t1.ONE_VOTE_ITEM =0 "); 
        	}
        }
        //isInspector 质检员看自己数据时才传的参数
  		if(CommonUtil.isNotBlank(param.getString("isInspector")) || param.getBooleanValue("isInspector")) {
  			//质检员查看数据
  			sql.append(userId, " and t1.INSPECTOR_ID = ?");
  		}else if(!StringUtil.equals("1", isExcellent)) {// 不是查的优秀录音页面需要数据权限
  			//使用数据权限
  			//原权限为 ： 如果是坐席，只能看到自己被质检结果；质检员，只能看到自己质检结果；  现在改为使用统一的数据权限
  	        // 获取数据权限
  	 		JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(user.getEpCode(), user.getBusiOrderId(), user.getUserAcc() + "~" + "cc-qc-zjjg"); // ‘人工质检结果’菜单目录ID
  	 		StringBuilder dataAuthSql = new StringBuilder();
  	 		if(obj != null) {
  	 			String depts = obj.getString("depts");
  	 			dataAuthSql.append(" AND ( T4.SERIAL_ID='-1' ");
  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
  	 				dataAuthSql.append(" OR T4.AGENT_DEPT IN ('" + String.join("','", depts.split(",")) + "')");
  	 			}
  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
  	 				dataAuthSql.append(" OR T4.AGENT_ID = '" + user.getUserId() + "'");
  	 			}
  	 			dataAuthSql.append(")");
  	 			if(StringUtils.isNotBlank(obj.getString("all"))) {
  	 				dataAuthSql = new StringBuilder();
  	 			}
  	 		} else {
  				dataAuthSql.append(" AND T4.SERIAL_ID='-1' ");
  			}
  			sql.append(dataAuthSql.toString());
  		}
        
        sql.append(" union all " );
        //全媒体
        sql.append("select T5.SKILL_GROUP_NAME DEPT_NAME,t3.TEMPLATE_ID,t3.RG_CLASS_ID ,t2.SERVICE_TIME,T4.CREATE_CAUSE CREATE_CAUSE1,T4.CREATE_CAUSE CREATE_CAUSE2,t1.ONE_VOTE_ITEM");
		sql.append(",t3.ZN_CLASS_ID,t1.QC_RESULT_ID,t1.DATE_ID,t1.INSPECTOR_ID,t1.INSPECTOR_NAME,t1.QC_TIME,t1.EXAM_GROUP_ID,t1.AGENT_ID,t1.AGENT_NAME,t1.CALL_TYPE,T1.EVALUATE,T1.EVALUATE2,T1.EVALUATE3   ");
		sql.append(",t1.TASK_ID,t1.TASK_NAME,t1.PASS_SCORE ,t1.SCORE,CASE WHEN t1.SCORE IS NOT NULL THEN t1.SCORE ELSE t2.ZN_QC_SCORE END AS ACTUAL_SCORE,t1.PASS_FLAG,t1.RESULT_DESC,t1.APPROVAL_STATE,t1.RESULT_CONTENT,t1.BASE_SCORE,t2.SERIAL_ID,t2.ID AS OBJ_ID,t2.ZN_QC_SCORE ZN_SCORE");
		sql.append(",t1.CACH_RESULT,t1.CACH_EXJOSN,t1.EVALUATE_EXPLAIN,t2.OBJ_TYPE,T4.CUST_CODE AS CALLER,T4.SERIAL_ID AS CHANNEL_SERIAL_ID,t2.BUSI_TYPE");
		sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT t1"));
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ t2") + " ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK t3") + " ON T3.ID = t2.TASK_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_MEDIA_RECORD") + " T4 ON T4.SERIAL_ID = t2.SERIAL_ID AND t2.CHANNEL_TYPE='2'");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T2.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T2.ENT_ID=T5.ENT_ID");
		sql.append("where 1=1");
		
		//评定等级
		sql.append(param.getString("evaluate"), " and t1.EVALUATE = ?");
		sql.append(param.getString("evaluate2"), " and t1.EVALUATE2 = ?");
		sql.append(param.getString("evaluate3"), " and t1.EVALUATE3 = ?");
		
		sql.append("and t2.CHANNEL_TYPE='2'");
		sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
		sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		if(StringUtil.equals("Y", openIssue)) {
			//开启发布功能只查状态为4的数据
			sql.append(" and t2.RG_STATE = '4'");
		}else {
			sql.append(" and t2.RG_STATE in ('3', '4') ");
		}
		
		//名单类型 0未分类 1优秀案例 2警示案例
		sql.append(param.getString("objType"), "and t2.OBJ_TYPE = ?");
				
		
		sql.append(param.getInteger("passFlag"), " and t1.PASS_FLAG = ?");
		sql.append(param.getInteger("callType"), " and t1.CALL_TYPE = ?");
		sql.append(param.getString("approvalState"), "and t1.APPROVAL_STATE = ?");
		sql.append(param.getString("cachResult"), "and t1.CACH_RESULT = ?");
		if(StringUtils.isNotBlank(startDate)){
			sql.append(startDate + " 00:00:00", " and t1.QC_TIME >= ?" );
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(endDate + " 23:59:59", " and t1.QC_TIME <= ?");
		}
		/*if(beforeNum != 0){
			sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), " and t1.DATE_ID <= ?");
		}*/
		if(StringUtils.isNotBlank(begin_time)){
			sql.append(begin_time+" 00:00:00","and t2.SERVICE_TIME >=?");
		}
		if(StringUtils.isNotBlank(end_time)){
			sql.append(end_time+" 23:59:59", " and t2.SERVICE_TIME <=?");
		}
		sql.append(task," AND t1.TASK_ID = ? ");
		if(StringUtil.equals("1", isExcellent)) {
			sql.append(excellentGradeCode," AND t1.EVALUATE = ? ");
		}
		
		sql.appendLike(param.getString("agentName"), " and t1.AGENT_NAME like ?");
		sql.append(param.getString("examGroupId"), " and t1.EXAM_GROUP_ID = ?");
		sql.append(param.getString("taskId"), " and t1.TASK_ID = ?");
		sql.append(param.getString("busiType")," and t2.BUSI_TYPE = ?");
        if(StringUtils.isNotBlank(oneVoteItem)){
        	if("Y".equals(oneVoteItem)){
        		sql.append(" and t1.ONE_VOTE_ITEM >0"); 
        	}else if("N".equals(oneVoteItem)){
        		sql.append(" and t1.ONE_VOTE_ITEM =0 "); 
        	}
        }
        
        
        
        //isInspector 质检员看自己数据时才传的参数
        if(CommonUtil.isNotBlank(param.getString("isInspector")) || param.getBooleanValue("isInspector")) {
			//质检员查看数据
			sql.append(userId, " and t1.INSPECTOR_ID = ?");
		}else if(!StringUtil.equals("1", isExcellent)) {// 不是查的优秀录音页面需要数据权限
  			//使用数据权限
  			//原权限为 ： 如果是坐席，只能看到自己被质检结果；质检员，只能看到自己质检结果；  现在改为使用统一的数据权限
  	        // 获取数据权限
  	 		JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(user.getEpCode(), user.getBusiOrderId(), user.getUserAcc() + "~" + "cc-qc-zjjg"); // ‘人工质检结果’菜单目录ID
  	 		StringBuilder dataAuthSql = new StringBuilder();
  	 		if(obj != null) {
  	 			String depts = obj.getString("depts");
  	 			dataAuthSql.append(" AND ( T4.SERIAL_ID='-1' ");
  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
  	 				dataAuthSql.append(" OR T4.AGENT_DEPT IN ('" + String.join("','", depts.split(",")) + "')");
  	 			}
  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
  	 				dataAuthSql.append(" OR T4.AGENT_ID = '" + user.getUserAcc() + "'");
  	 			}
  	 			dataAuthSql.append(")");
  	 			if(StringUtils.isNotBlank(obj.getString("all"))) {
  	 				dataAuthSql = new StringBuilder();
  	 			}
  	 		} else {
  				dataAuthSql.append(" AND T4.SERIAL_ID='-1' ");
  			}
  			sql.append(dataAuthSql.toString());
  		}
        
        
		String emailOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_EMAIL_QC");

		if("Y".equals(emailOpen)) {
			   //邮件
	        sql.append(" union all " );
	        
	        sql.append("select T5.SKILL_GROUP_NAME DEPT_NAME,t3.TEMPLATE_ID,t3.RG_CLASS_ID ,t2.SERVICE_TIME,-1 as  CREATE_CAUSE1,-1 as  CREATE_CAUSE2,t1.ONE_VOTE_ITEM");
			sql.append(",t3.ZN_CLASS_ID,t1.QC_RESULT_ID,t1.DATE_ID,t1.INSPECTOR_ID,t1.INSPECTOR_NAME,t1.QC_TIME,t1.EXAM_GROUP_ID,t1.AGENT_ID,t1.AGENT_NAME,t1.CALL_TYPE,T1.EVALUATE,T1.EVALUATE2,T1.EVALUATE3   ");
			sql.append(",t1.TASK_ID,t1.TASK_NAME,t1.PASS_SCORE ,t1.SCORE,CASE WHEN t1.SCORE IS NOT NULL THEN t1.SCORE ELSE t2.ZN_QC_SCORE END AS ACTUAL_SCORE,t1.PASS_FLAG,t1.RESULT_DESC,t1.APPROVAL_STATE,t1.RESULT_CONTENT,t1.BASE_SCORE,t2.SERIAL_ID,t2.ID AS OBJ_ID,t2.ZN_QC_SCORE ZN_SCORE");
			sql.append(",t1.CACH_RESULT,t1.CACH_EXJOSN,t1.EVALUATE_EXPLAIN,t2.OBJ_TYPE,T4.EMAIL_FROM AS CALLER,T4.ID AS CHANNEL_SERIAL_ID,t2.BUSI_TYPE");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT t1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ t2") + " ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK t3") + " ON T3.ID = t2.TASK_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_EMAIL_SESSION") + " T4 ON T4.ID = t2.SERIAL_ID AND t2.CHANNEL_TYPE='3'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T2.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T2.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");
			
			//评定等级
			sql.append(param.getString("evaluate"), " and t1.EVALUATE = ?");
			sql.append(param.getString("evaluate2"), " and t1.EVALUATE2 = ?");
			sql.append(param.getString("evaluate3"), " and t1.EVALUATE3 = ?");
			
			sql.append("and t2.CHANNEL_TYPE='3'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and t2.RG_STATE = '4'");
			}else {
				sql.append(" and t2.RG_STATE in ('3', '4') ");
			}
			
			//名单类型 0未分类 1优秀案例 2警示案例
			sql.append(param.getString("objType"), "and t2.OBJ_TYPE = ?");
			
			sql.append(param.getInteger("passFlag"), " and t1.PASS_FLAG = ?");
			sql.append(param.getInteger("callType"), " and t1.CALL_TYPE = ?");
			sql.append(param.getString("approvalState"), "and t1.APPROVAL_STATE = ?");
			sql.append(param.getString("cachResult"), "and t1.CACH_RESULT = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and t1.QC_TIME >= ?" );
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and t1.QC_TIME <= ?");
			}
			/*if(beforeNum != 0){
				sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), " and t1.DATE_ID <= ?");
			}*/
			if(StringUtils.isNotBlank(begin_time)){
				sql.append(begin_time+" 00:00:00","and t2.SERVICE_TIME >=?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " and t2.SERVICE_TIME <=?");
			}
			sql.append(task," AND t1.TASK_ID = ? ");
			if(StringUtil.equals("1", isExcellent)) {
				sql.append(excellentGradeCode," AND t1.EVALUATE = ? ");
			}
			sql.appendLike(param.getString("agentName"), " and t1.AGENT_NAME like ?");
			sql.append(param.getString("examGroupId"), " and t1.EXAM_GROUP_ID = ?");
			sql.append(param.getString("taskId"), " and t1.TASK_ID = ?");
			sql.append(param.getString("busiType")," and t2.BUSI_TYPE = ?");
	        if(StringUtils.isNotBlank(oneVoteItem)){
	        	if("Y".equals(oneVoteItem)){
	        		sql.append(" and t1.ONE_VOTE_ITEM >0"); 
	        	}else if("N".equals(oneVoteItem)){
	        		sql.append(" and t1.ONE_VOTE_ITEM =0 "); 
	        	}
	        }
	        
	        //isInspector 质检员看自己数据时才传的参数
	        if(CommonUtil.isNotBlank(param.getString("isInspector")) || param.getBooleanValue("isInspector")) {
				//质检员查看数据
				sql.append(userId, " and t1.INSPECTOR_ID = ?");
			}else if(!StringUtil.equals("1", isExcellent)) {// 不是查的优秀录音页面需要数据权限
	  			//使用数据权限
	  			//原权限为 ： 如果是坐席，只能看到自己被质检结果；质检员，只能看到自己质检结果；  现在改为使用统一的数据权限
	  	        // 获取数据权限
	  	 		JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(user.getEpCode(), user.getBusiOrderId(), user.getUserAcc() + "~" + "cc-qc-zjjg"); // ‘人工质检结果’菜单目录ID
	  	 		StringBuilder dataAuthSql = new StringBuilder();
	  	 		if(obj != null) {
	  	 			String depts = obj.getString("depts");
	  	 			dataAuthSql.append(" AND ( T4.ID='-1' ");
	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
	  	 				dataAuthSql.append(" OR T4.FIRST_HANDLE_DEPT_CODE IN ('" + String.join("','", depts.split(",")) + "')");
	  	 			}
	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
	  	 				dataAuthSql.append(" OR T4.HANDLE_ACC = '" + user.getUserAcc() + "'");
	  	 			}
	  	 			dataAuthSql.append(")");
	  	 			if(StringUtils.isNotBlank(obj.getString("all"))) {
	  	 				dataAuthSql = new StringBuilder();
	  	 			}
	  	 		} else {
	  				dataAuthSql.append(" AND T4.ID='-1' ");
	  			}
	  			sql.append(dataAuthSql.toString());
	  		}
		}
     
  
		String thirdOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OEPN_THIRD_PARTY_QC");

		if("Y".equals(thirdOpen)) {

	        sql.append(" union all " );
	        //第三方
	        sql.append("select T5.SKILL_GROUP_NAME DEPT_NAME,t3.TEMPLATE_ID,t3.RG_CLASS_ID ,t2.SERVICE_TIME,-1 as CREATE_CAUSE1,-1 as CREATE_CAUSE2,t1.ONE_VOTE_ITEM");//T3.TEMPLATE_ID as CREATE_CAUSE1,'' as CREATE_CAUSE2
			sql.append(",t3.ZN_CLASS_ID,t1.QC_RESULT_ID,t1.DATE_ID,t1.INSPECTOR_ID,t1.INSPECTOR_NAME,t1.QC_TIME,t1.EXAM_GROUP_ID,t1.AGENT_ID,t1.AGENT_NAME,t1.CALL_TYPE,T1.EVALUATE,T1.EVALUATE2,T1.EVALUATE3   ");
			sql.append(",t1.TASK_ID,t1.TASK_NAME,t1.PASS_SCORE ,t1.SCORE,CASE WHEN t1.SCORE IS NOT NULL THEN t1.SCORE ELSE t2.ZN_QC_SCORE END AS ACTUAL_SCORE,t1.PASS_FLAG,t1.RESULT_DESC,t1.APPROVAL_STATE,t1.RESULT_CONTENT,t1.BASE_SCORE,t2.SERIAL_ID,t2.ID AS OBJ_ID,t2.ZN_QC_SCORE ZN_SCORE");
			sql.append(",t1.CACH_RESULT,t1.CACH_EXJOSN,t1.EVALUATE_EXPLAIN,t2.OBJ_TYPE,T4.CALLER,T4.OBJECT_ID AS CHANNEL_SERIAL_ID,t2.BUSI_TYPE");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT t1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ t2") + " ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK t3") + " ON T3.ID = t2.TASK_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_THIRD_RECORD") + " T4 ON T4.SERIAL_ID = t2.SERIAL_ID AND t2.CHANNEL_TYPE='9'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T2.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T2.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");
			
			//评定等级
			sql.append(param.getString("evaluate"), " and t1.EVALUATE = ?");
			sql.append(param.getString("evaluate2"), " and t1.EVALUATE2 = ?");
			sql.append(param.getString("evaluate3"), " and t1.EVALUATE3 = ?");
			sql.append("and t2.CHANNEL_TYPE='9'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and t2.RG_STATE = '4'");
			}else {
				sql.append(" and t2.RG_STATE in ('3', '4') ");
			}
			
			//名单类型 0未分类 1优秀案例 2警示案例
			sql.append(param.getString("objType"), "and t2.OBJ_TYPE = ?");
			
			sql.append(param.getInteger("passFlag"), " and t1.PASS_FLAG = ?");
			sql.append(param.getInteger("callType"), " and t1.CALL_TYPE = ?");
			sql.append(param.getString("approvalState"), "and t1.APPROVAL_STATE = ?");
			sql.append(param.getString("cachResult"), "and t1.CACH_RESULT = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and t1.QC_TIME >= ?" );
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and t1.QC_TIME <= ?");
			}
			/*if(beforeNum != 0){
				sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), " and t1.DATE_ID <= ?");
			}*/
			if(StringUtils.isNotBlank(begin_time)){
				sql.append(begin_time+" 00:00:00","and t2.SERVICE_TIME >=?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " and t2.SERVICE_TIME <=?");
			}
			sql.append(task," AND t1.TASK_ID = ? ");
			if(StringUtil.equals("1", isExcellent)) {
				sql.append(excellentGradeCode," AND t1.EVALUATE = ? ");
			}
			sql.appendLike(param.getString("agentName"), " and t1.AGENT_NAME like ?");
			sql.append(param.getString("examGroupId"), " and t1.EXAM_GROUP_ID = ?");
			sql.append(param.getString("taskId"), " and t1.TASK_ID = ?");
			sql.append(param.getString("busiType")," and t2.BUSI_TYPE = ?");
	        if(StringUtils.isNotBlank(oneVoteItem)){
	        	if("Y".equals(oneVoteItem)){
	        		sql.append(" and t1.ONE_VOTE_ITEM >0"); 
	        	}else if("N".equals(oneVoteItem)){
	        		sql.append(" and t1.ONE_VOTE_ITEM =0 "); 
	        	}
	        }
	        
	        //isInspector 质检员看自己数据时才传的参数
	        if(CommonUtil.isNotBlank(param.getString("isInspector")) || param.getBooleanValue("isInspector")) {
				//质检员查看数据
				sql.append(userId, " and t1.INSPECTOR_ID = ?");
			}else if(!StringUtil.equals("1", isExcellent)) {// 不是查的优秀录音页面需要数据权限
	  			//使用数据权限
	  			//原权限为 ： 如果是坐席，只能看到自己被质检结果；质检员，只能看到自己质检结果；  现在改为使用统一的数据权限
	  	        // 获取数据权限
	  	 		/*JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(user.getEpCode(), user.getBusiOrderId(), user.getUserAcc() + "~" + "cc-qc-zjjg"); // ‘人工质检结果’菜单目录ID
	  	 		StringBuilder dataAuthSql = new StringBuilder();
	  	 		if(obj != null) {
	  	 			String depts = obj.getString("depts");
	  	 			dataAuthSql.append(" AND ( T2.SERIAL_ID='-1' ");
	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
	  	 				dataAuthSql.append(" OR T3.AGENT_DEPT_CODE IN ('" + String.join("','", depts.split(",")) + "')");
	  	 			}
	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
	  	 				dataAuthSql.append(" OR T3.AGENT_ACC = '" + user.getUserAcc() + "'");
	  	 			}
	  	 			dataAuthSql.append(")");
	  	 			if(StringUtils.isNotBlank(obj.getString("all"))) {
	  	 				dataAuthSql = new StringBuilder();
	  	 			}
	  	 		} else {
	  				dataAuthSql.append(" AND T3.SERIAL_ID='-1' ");
	  			}
	  			sql.append(dataAuthSql.toString())*/;
	  		}
	        
		}
		
		String orderOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OEPN_ORDER_QC");
		logger.info("是否开启工单质检:"+orderOpen);
		if("Y".equals(orderOpen)) {

	        sql.append(" union all " );
	        //工单
	        sql.append("select T5.SKILL_GROUP_NAME DEPT_NAME,t3.TEMPLATE_ID,t3.RG_CLASS_ID ,t2.SERVICE_TIME,'' as CREATE_CAUSE1,'' as CREATE_CAUSE2,t1.ONE_VOTE_ITEM");//T3.TEMPLATE_ID as CREATE_CAUSE1,'' as CREATE_CAUSE2
			sql.append(",t3.ZN_CLASS_ID,t1.QC_RESULT_ID,t1.DATE_ID,t1.INSPECTOR_ID,t1.INSPECTOR_NAME,t1.QC_TIME,t1.EXAM_GROUP_ID,t1.AGENT_ID,t1.AGENT_NAME,t1.CALL_TYPE,T1.EVALUATE,T1.EVALUATE2,T1.EVALUATE3   ");
			sql.append(",t1.TASK_ID,t1.TASK_NAME,t1.PASS_SCORE ,t1.SCORE,CASE WHEN t1.SCORE IS NOT NULL THEN t1.SCORE ELSE t2.ZN_QC_SCORE END AS ACTUAL_SCORE,t1.PASS_FLAG,t1.RESULT_DESC,t1.APPROVAL_STATE,t1.RESULT_CONTENT,t1.BASE_SCORE,t2.SERIAL_ID,t2.ID AS OBJ_ID,t2.ZN_QC_SCORE ZN_SCORE");
			sql.append(",t1.CACH_RESULT,t1.CACH_EXJOSN,t1.EVALUATE_EXPLAIN,t2.OBJ_TYPE,T4.CALLER,T4.ORDER_NO AS CHANNEL_SERIAL_ID,t2.BUSI_TYPE");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT t1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ t2") + " ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK t3") + " ON T3.ID = t2.TASK_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_BO_BASE_ORDER") + " T4 ON T4.ID = t2.SERIAL_ID AND t2.CHANNEL_TYPE='4'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T2.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T2.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");
			
			//评定等级
			sql.append(param.getString("evaluate"), " and t1.EVALUATE = ?");
			sql.append(param.getString("evaluate2"), " and t1.EVALUATE2 = ?");
			sql.append(param.getString("evaluate3"), " and t1.EVALUATE3 = ?");
			
			sql.append("and t2.CHANNEL_TYPE='4'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and t2.RG_STATE = '4'");
			}else {
				sql.append(" and t2.RG_STATE in ('3', '4') ");
			}
			
			//名单类型 0未分类 1优秀案例 2警示案例
			sql.append(param.getString("objType"), "and t2.OBJ_TYPE = ?");
			
			sql.append(param.getInteger("passFlag"), " and t1.PASS_FLAG = ?");
			sql.append(param.getInteger("callType"), " and t1.CALL_TYPE = ?");
			sql.append(param.getString("approvalState"), "and t1.APPROVAL_STATE = ?");
			sql.append(param.getString("cachResult"), "and t1.CACH_RESULT = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and t1.QC_TIME >= ?" );
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and t1.QC_TIME <= ?");
			}
			//防止出现重复工单数据
			sql.append(user.getEpCode(), "AND T4.EP_CODE=?");
			/*if(beforeNum != 0){
				sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), " and t1.DATE_ID <= ?");
			}*/
			if(StringUtils.isNotBlank(begin_time)){
				sql.append(begin_time+" 00:00:00","and t2.SERVICE_TIME >=?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " and t2.SERVICE_TIME <=?");
			}
			sql.append(task," AND t1.TASK_ID = ? ");
			if(StringUtil.equals("1", isExcellent)) {
				sql.append(excellentGradeCode," AND t1.EVALUATE = ? ");
			}
			sql.appendLike(param.getString("agentName"), " and t1.AGENT_NAME like ?");
			sql.append(param.getString("examGroupId"), " and t1.EXAM_GROUP_ID = ?");
			sql.append(param.getString("taskId"), " and t1.TASK_ID = ?");
			sql.append(param.getString("busiType")," and t2.BUSI_TYPE = ?");
	        if(StringUtils.isNotBlank(oneVoteItem)){
	        	if("Y".equals(oneVoteItem)){
	        		sql.append(" and t1.ONE_VOTE_ITEM >0"); 
	        	}else if("N".equals(oneVoteItem)){
	        		sql.append(" and t1.ONE_VOTE_ITEM =0 "); 
	        	}
	        }
	        
	        //isInspector 质检员看自己数据时才传的参数
	        if(CommonUtil.isNotBlank(param.getString("isInspector")) || param.getBooleanValue("isInspector")) {
				//质检员查看数据
				sql.append(userId, " and t1.INSPECTOR_ID = ?");
			}else if(!StringUtil.equals("1", isExcellent)) {// 不是查的优秀录音页面需要数据权限
	  			//使用数据权限
	  			//原权限为 ： 如果是坐席，只能看到自己被质检结果；质检员，只能看到自己质检结果；  现在改为使用统一的数据权限
	  	        // 获取数据权限
	  	 		/*JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(user.getEpCode(), user.getBusiOrderId(), user.getUserAcc() + "~" + "cc-qc-zjjg"); // ‘人工质检结果’菜单目录ID
	  	 		StringBuilder dataAuthSql = new StringBuilder();
	  	 		if(obj != null) {
	  	 			String depts = obj.getString("depts");
	  	 			dataAuthSql.append(" AND ( T3.SERIAL_ID='-1' ");
	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
	  	 				dataAuthSql.append(" OR T3.AGENT_DEPT_CODE IN ('" + String.join("','", depts.split(",")) + "')");
	  	 			}
	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
	  	 				dataAuthSql.append(" OR T3.AGENT_ACC = '" + user.getUserAcc() + "'");
	  	 			}
	  	 			dataAuthSql.append(")");
	  	 			if(StringUtils.isNotBlank(obj.getString("all"))) {
	  	 				dataAuthSql = new StringBuilder();
	  	 			}
	  	 		} else {
	  				dataAuthSql.append(" AND T3.SERIAL_ID='-1' ");
	  			}
	  			sql.append(dataAuthSql.toString());*/
	  		}
			sql.append(" union all " );
			//工单老化表
			sql.append("select T5.SKILL_GROUP_NAME DEPT_NAME,t3.TEMPLATE_ID,t3.RG_CLASS_ID ,t2.SERVICE_TIME,'' as CREATE_CAUSE1,'' as CREATE_CAUSE2,t1.ONE_VOTE_ITEM");//T3.TEMPLATE_ID as CREATE_CAUSE1,'' as CREATE_CAUSE2
			sql.append(",t3.ZN_CLASS_ID,t1.QC_RESULT_ID,t1.DATE_ID,t1.INSPECTOR_ID,t1.INSPECTOR_NAME,t1.QC_TIME,t1.EXAM_GROUP_ID,t1.AGENT_ID,t1.AGENT_NAME,t1.CALL_TYPE,T1.EVALUATE,T1.EVALUATE2,T1.EVALUATE3   ");
			sql.append(",t1.TASK_ID,t1.TASK_NAME,t1.PASS_SCORE ,t1.SCORE,CASE WHEN t1.SCORE IS NOT NULL THEN t1.SCORE ELSE t2.ZN_QC_SCORE END AS ACTUAL_SCORE,t1.PASS_FLAG,t1.RESULT_DESC,t1.APPROVAL_STATE,t1.RESULT_CONTENT,t1.BASE_SCORE,t2.SERIAL_ID,t2.ID AS OBJ_ID,t2.ZN_QC_SCORE ZN_SCORE");
			sql.append(",t1.CACH_RESULT,t1.CACH_EXJOSN,t1.EVALUATE_EXPLAIN,t2.OBJ_TYPE,T4.CALLER,T4.ORDER_NO AS CHANNEL_SERIAL_ID,t2.BUSI_TYPE");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT t1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ t2") + " ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK t3") + " ON T3.ID = t2.TASK_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_BO_BASE_ORDER_HIS") + " T4 ON T4.ID = t2.SERIAL_ID AND t2.CHANNEL_TYPE='4'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T2.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T2.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");

			//评定等级
			sql.append(param.getString("evaluate"), " and t1.EVALUATE = ?");
			sql.append(param.getString("evaluate2"), " and t1.EVALUATE2 = ?");
			sql.append(param.getString("evaluate3"), " and t1.EVALUATE3 = ?");

			sql.append("and t2.CHANNEL_TYPE='4'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			if(StringUtil.equals("Y", openIssue)) {
				//开启发布功能只查状态为4的数据
				sql.append(" and t2.RG_STATE = '4'");
			}else {
				sql.append(" and t2.RG_STATE in ('3', '4') ");
			}

			//名单类型 0未分类 1优秀案例 2警示案例
			sql.append(param.getString("objType"), "and t2.OBJ_TYPE = ?");

			sql.append(param.getInteger("passFlag"), " and t1.PASS_FLAG = ?");
			sql.append(param.getInteger("callType"), " and t1.CALL_TYPE = ?");
			sql.append(param.getString("approvalState"), "and t1.APPROVAL_STATE = ?");
			sql.append(param.getString("cachResult"), "and t1.CACH_RESULT = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(startDate + " 00:00:00", " and t1.QC_TIME >= ?" );
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(endDate + " 23:59:59", " and t1.QC_TIME <= ?");
			}
			//防止出现重复工单数据
			sql.append(user.getEpCode(), "AND T4.EP_CODE=?");
			/*if(beforeNum != 0){
				sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), " and t1.DATE_ID <= ?");
			}*/
			if(StringUtils.isNotBlank(begin_time)){
				sql.append(begin_time+" 00:00:00","and t2.SERVICE_TIME >=?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " and t2.SERVICE_TIME <=?");
			}
			sql.append(task," AND t1.TASK_ID = ? ");
			if(StringUtil.equals("1", isExcellent)) {
				sql.append(excellentGradeCode," AND t1.EVALUATE = ? ");
			}
			sql.appendLike(param.getString("agentName"), " and t1.AGENT_NAME like ?");
			sql.append(param.getString("examGroupId"), " and t1.EXAM_GROUP_ID = ?");
			sql.append(param.getString("taskId"), " and t1.TASK_ID = ?");
			sql.append(param.getString("busiType")," and t2.BUSI_TYPE = ?");
			if(StringUtils.isNotBlank(oneVoteItem)){
				if("Y".equals(oneVoteItem)){
					sql.append(" and t1.ONE_VOTE_ITEM >0");
				}else if("N".equals(oneVoteItem)){
					sql.append(" and t1.ONE_VOTE_ITEM =0 ");
				}
			}

			//isInspector 质检员看自己数据时才传的参数
			if(CommonUtil.isNotBlank(param.getString("isInspector")) || param.getBooleanValue("isInspector")) {
				//质检员查看数据
				sql.append(userId, " and t1.INSPECTOR_ID = ?");
			}

		}

        sql.append(") TT ");
        sql.append("left join  "+CommonUtil.getTableName(user.getSchemaName(),"cc_qc_admin_select")+" TT2 ON TT2.TASK_OBJ_ID = TT.OBJ_ID");
		String sortName = param.getString("sortName");
		String sortType = param.getString("sortType");
		if(StringUtils.isBlank(sortType) || StringUtils.isBlank(sortName)) {
			sql.append(" ORDER BY  TT.QC_TIME desc,TT.DATE_ID desc ");
		}else {
			sql.appendSort(sortName, sortType, " TT.QC_TIME desc,TT.DATE_ID desc");
		}
		return sql;
	}


	//获取人工质检结果sql (自定义渠道)
	public static EasySQL getRgResultListSqlByThirdPartySql(UserModel user, JSONObject param, HttpServletRequest request, EasyQuery query) {
		//第三方
		String openIssue =  SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_ISSUE");
		EasySQL sql = new EasySQL();
		String begin_time = param.getString("beginServiceTime");
		String end_time = param.getString("endServiceTime");
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		String task = param.getString("task");
		String isExcellent = param.getString("isExcellent");
		String excellentGradeCode = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "EXCELLENT_GRADE_CODE");
		String userId = user.getUserId();
		String oneVoteItem = param.getString("oneVoteItem");
		String templateId = param.getString("templateId");

		sql.append("select T5.SKILL_GROUP_NAME DEPT_NAME,t3.TEMPLATE_ID,t3.RG_CLASS_ID ,t2.SERVICE_TIME,-1 as CREATE_CAUSE1,-1 as CREATE_CAUSE2,t1.ONE_VOTE_ITEM");//T3.TEMPLATE_ID as CREATE_CAUSE1,'' as CREATE_CAUSE2
		sql.append(",t3.ZN_CLASS_ID,t1.QC_RESULT_ID,t1.DATE_ID,t1.INSPECTOR_ID,t1.INSPECTOR_NAME,t1.QC_TIME,t1.EXAM_GROUP_ID,t1.AGENT_ID,t1.AGENT_NAME,t1.CALL_TYPE,T1.EVALUATE,T1.EVALUATE2,T1.EVALUATE3   ");
		sql.append(",t1.TASK_ID,t1.TASK_NAME,t1.PASS_SCORE ,t1.SCORE,CASE WHEN t1.SCORE IS NOT NULL THEN t1.SCORE ELSE t2.ZN_QC_SCORE END AS ACTUAL_SCORE,t1.PASS_FLAG,t1.RESULT_DESC,t1.APPROVAL_STATE,t1.RESULT_CONTENT,t1.BASE_SCORE,t2.SERIAL_ID,t2.ID AS OBJ_ID,t2.ZN_QC_SCORE ZN_SCORE");
		sql.append(",t1.CACH_RESULT,t1.CACH_EXJOSN,t1.EVALUATE_EXPLAIN,t2.OBJ_TYPE,T4.CALLER,T4.CALLED,T4.OBJECT_ID AS CHANNEL_SERIAL_ID,t2.BUSI_TYPE,t2.AGENT_ACC,T6.IS_REVIEW ");

		// 添加第三方动态字段
		if (StringUtils.isNotBlank(templateId)) {
			// 获取可查询字段
			ThirdPartyFieldCache thirdFieldCache = new ThirdPartyFieldCache();
			JSONObject fieldCache = thirdFieldCache.getCache(user.getEpCode(), user.getBusiOrderId(), templateId);
			
			// 添加动态字段到SELECT部分
			JSONArray queryForList = fieldCache.getJSONArray("showList");
			for (int i = 0; i < queryForList.size(); i++) {
				JSONObject jsonObject = queryForList.getJSONObject(i);
				
				if ("TEMPLATE_ID".equals(jsonObject.getString("FIELD_EN"))) {
					continue;
				}
				
				sql.append(",T4."+jsonObject.getString("FIELD_EN"));
				sql.append(" as THIRD_"+jsonObject.getString("FIELD_EN"));
			}
		}

		sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT t1"));
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ t2") + " ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK t3") + " ON T3.ID = t2.TASK_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_THIRD_RECORD") + " T4 ON T4.SERIAL_ID = t2.SERIAL_ID AND t2.CHANNEL_TYPE='9'");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T2.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T2.ENT_ID=T5.ENT_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_CAPACITY") + " T6 ON T6.SERIAL_ID = T2.SERIAL_ID AND T6.ZN_CLASS_ID = T3.ZN_CLASS_ID   ");
		sql.append("where 1=1");
		sql.append(templateId," and t3.template_id = ?");

		//评定等级
		sql.append(param.getString("evaluate"), " and t1.EVALUATE = ?");
		sql.append(param.getString("evaluate2"), " and t1.EVALUATE2 = ?");
		sql.append(param.getString("evaluate3"), " and t1.EVALUATE3 = ?");
		sql.append("and t2.CHANNEL_TYPE='9'");
		sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
		sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		if(StringUtil.equals("Y", openIssue)) {
			//开启发布功能只查状态为4的数据
			sql.append(" and t2.RG_STATE = '4'");
		}else {
			sql.append(" and t2.RG_STATE in ('3', '4') ");
		}

		//名单类型 0未分类 1优秀案例 2警示案例
		sql.append(param.getString("objType"), "and t2.OBJ_TYPE = ?");

		sql.append(param.getInteger("passFlag"), " and t1.PASS_FLAG = ?");
		sql.append(param.getInteger("callType"), " and t1.CALL_TYPE = ?");
		sql.append(param.getString("approvalState"), "and t1.APPROVAL_STATE = ?");
		sql.append(param.getString("cachResult"), "and t1.CACH_RESULT = ?");
		if(StringUtils.isNotBlank(startDate)){
			sql.append(startDate, " and t1.QC_TIME >= ?" );
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(endDate, " and t1.QC_TIME <= ?");
		}
			/*if(beforeNum != 0){
				sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), " and t1.DATE_ID <= ?");
			}*/
		if(StringUtils.isNotBlank(begin_time)){
			sql.append(begin_time,"and t2.SERVICE_TIME >=?");
		}
		if(StringUtils.isNotBlank(end_time)){
			sql.append(end_time, " and t2.SERVICE_TIME <=?");
		}
		sql.append(task," AND t1.TASK_ID = ? ");
		if(StringUtil.equals("1", isExcellent)) {
			sql.append(excellentGradeCode," AND t1.EVALUATE = ? ");
		}
		sql.append(param.getString("agentName"), " and t2.AGENT_ACC = ?");
		sql.append(param.getString("examGroupId"), " and t1.EXAM_GROUP_ID = ?");
		sql.append(param.getString("taskId"), " and t1.TASK_ID = ?");
		sql.append(param.getString("busiType")," and t2.BUSI_TYPE = ?");
		if(StringUtils.isNotBlank(oneVoteItem)){
			if("Y".equals(oneVoteItem)){
				sql.append(" and t1.ONE_VOTE_ITEM >0");
			}else if("N".equals(oneVoteItem)){
				sql.append(" and t1.ONE_VOTE_ITEM =0 ");
			}
		}

		//isInspector 质检员看自己数据时才传的参数
		if(CommonUtil.isNotBlank(param.getString("isInspector")) || param.getBooleanValue("isInspector")) {
			//质检员查看数据
			sql.append(userId, " and t1.INSPECTOR_ID = ?");
		}

		// 添加第三方动态搜索条件
		if (StringUtils.isNotBlank(templateId)) {
			ThirdPartyFieldCache thirdFieldCache = new ThirdPartyFieldCache();
			JSONObject fieldCache = thirdFieldCache.getCache(user.getEpCode(), user.getBusiOrderId(), templateId);
			JSONArray searchForList = fieldCache.getJSONArray("searchList");
			
			// 使用SQLUtil.extendQuery2方法添加动态搜索条件
			SQLUtil.extendQuery2("T4.", param.getJSONObject("form"), searchForList, sql);
		}
		sql.append(" ORDER BY T1.QC_TIME desc ");
		return sql;
	}

	//获取待辅导列表sql
	public static EasySQL getCachListSql(UserModel user, JSONObject param, HttpServletRequest request, EasyQuery query) {
		//是否开启发布功能
		String openIssue =  SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_ISSUE");
		String openCoachScore =  SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_COACH_SCORE");

		EasySQL sql = new EasySQL("select TT.*,TT2.ID AS SELECT_ID,'' AS RG_EVALUATE_RESULT from (");
		//语音
		sql.append("select T5.SKILL_GROUP_NAME DEPT_NAME,t3.TEMPLATE_ID,t3.RG_CLASS_ID ,t2.SERVICE_TIME,T4.CREATE_CAUSE CREATE_CAUSE1,T4.CREATE_CAUSE CREATE_CAUSE2,t1.ONE_VOTE_ITEM");
		sql.append(",t3.ZN_CLASS_ID,t1.QC_RESULT_ID,t1.DATE_ID,t1.INSPECTOR_ID,t1.INSPECTOR_NAME,t1.QC_TIME,t1.EXAM_GROUP_ID,t1.AGENT_ID,t1.AGENT_NAME,t1.CALL_TYPE,T1.EVALUATE   ");
		sql.append(",t1.TASK_ID,t1.TASK_NAME,t1.PASS_SCORE ,t1.SCORE,CASE WHEN t1.SCORE IS NOT NULL THEN t1.SCORE ELSE t2.ZN_QC_SCORE END AS ACTUAL_SCORE,t1.PASS_FLAG,t1.RESULT_DESC,t1.APPROVAL_STATE,t1.RESULT_CONTENT,t1.BASE_SCORE,t2.SERIAL_ID,t2.ID AS OBJ_ID,t2.ZN_QC_SCORE ZN_SCORE");
		sql.append(",t1.CACH_RESULT,t1.CACH_STATE,t1.CACH_EXJOSN,t1.EVALUATE_EXPLAIN,t2.OBJ_TYPE,T4.CALLER,T4.SERIAL_ID AS CHANNEL_SERIAL_ID,t2.BUSI_TYPE");
		sql.append("from "+ CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT t1"));
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ t2") + " ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK t3") + " ON T3.ID = t2.TASK_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_CALL_RECORD") + " T4 ON T4.SERIAL_ID = t2.SERIAL_ID AND t2.CHANNEL_TYPE='1'");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T2.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T2.ENT_ID=T5.ENT_ID");
		sql.append("where 1=1");
		//评定等级
		sql.append(param.getString("evaluate"), " and t1.EVALUATE = ?");
		sql.append(param.getString("evaluate2"), " and t1.EVALUATE2 = ?");
		sql.append(param.getString("evaluate3"), " and t1.EVALUATE3 = ?");

		sql.append("and t2.CHANNEL_TYPE='1'");
		sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
		sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		//数据必须由坐席结案
		sql.append("and t1.APPROVAL_STATE = '5'");
		//需要辅导的数据
		sql.append("and t1.CACH_STATE != '0' ");
		if(StringUtil.equals("Y", openCoachScore)) {
			sql.append(Constants.getPassCoachScore(user.getSchemaName(),user.getEpCode(),user.getBusiOrderId())," and t1.SCORE <= ?");
		}
		//名单类型 0未分类 1优秀案例 2警示案例
		sql.append(param.getString("objType"), "and t2.OBJ_TYPE = ?");

		String userId = user.getUserId();
		sql.append(param.getInteger("passFlag"), " and t1.PASS_FLAG = ?");
		sql.append(param.getInteger("callType"), " and t1.CALL_TYPE = ?");
		sql.append(param.getString("cachState"), "and t1.CACH_STATE = ?");
//        sql.append(param.getString("approvalState"), "and t1.APPROVAL_STATE = ?");
		sql.append(param.getString("cachResult"), "and t1.CACH_RESULT = ?");
		sql.append(param.getString("busiType"),"and t2.BUSI_TYPE = ?");
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		if(StringUtils.isNotBlank(startDate)){
			//DATE_ID为int类型 postgresql需要类型转换
			sql.append(CommonUtil.parseInteger(startDate.replaceAll("-", "")), " and t1.DATE_ID >= ?");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(CommonUtil.parseInteger(endDate.replaceAll("-", "")), " and t1.DATE_ID <= ?");
		}
		/*if(beforeNum != 0){
			sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), " and t1.DATE_ID <= ?");
		}*/
		String begin_time = param.getString("beginServiceTime");
		String end_time = param.getString("endServiceTime");
		if(StringUtils.isNotBlank(begin_time)){
			sql.append(begin_time+" 00:00:00","and t2.SERVICE_TIME >=?");
		}
		if(StringUtils.isNotBlank(end_time)){
			sql.append(end_time+" 23:59:59", " and t2.SERVICE_TIME <=?");
		}
		String task = param.getString("task");
		if(CommonUtil.isNotBlank(task)) {
			String[] c_t = task.split(",");
			if(c_t != null && c_t.length > 1) {
				task = c_t[1];
			}
		}
		sql.append(task," AND t1.TASK_ID = ? ");
		String isExcellent = param.getString("isExcellent");
		String excellentGradeCode = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "EXCELLENT_GRADE_CODE");
		if(StringUtil.equals("1", isExcellent)) {
			//优秀录音
			sql.append(excellentGradeCode," AND t1.EVALUATE = ? ");
		}
		sql.appendLike(param.getString("agentName"), " and t1.AGENT_NAME like ?");
		sql.append(param.getString("examGroupId"), " and t1.EXAM_GROUP_ID = ?");
		sql.append(param.getString("taskId"), " and t1.TASK_ID = ?");
		String oneVoteItem = param.getString("oneVoteItem");
		if(StringUtils.isNotBlank(oneVoteItem)){
			if("Y".equals(oneVoteItem)){
				sql.append(" and t1.ONE_VOTE_ITEM >0");
			}else if("N".equals(oneVoteItem)){
				sql.append(" and t1.ONE_VOTE_ITEM =0 ");
			}
		}
		//isInspector 质检员看自己数据时才传的参数
		if(CommonUtil.isNotBlank(param.getString("isInspector")) || param.getBooleanValue("isInspector")) {
			//质检员查看数据
			sql.append(userId, " and t1.INSPECTOR_ID = ?");
		}else if(!StringUtil.equals("1", isExcellent)) {// 不是查的优秀录音页面需要数据权限
			//使用数据权限
			//原权限为 ： 如果是坐席，只能看到自己被质检结果；质检员，只能看到自己质检结果；  现在改为使用统一的数据权限
			// 获取数据权限
			JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(user.getEpCode(), user.getBusiOrderId(), user.getUserAcc() + "~" + "cc-qc-zjjg"); // ‘人工质检结果’菜单目录ID
			StringBuilder dataAuthSql = new StringBuilder();
			if(obj != null) {
				String depts = obj.getString("depts");
				dataAuthSql.append(" AND ( T4.SERIAL_ID='-1' ");
				if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
					dataAuthSql.append(" OR T4.AGENT_DEPT IN ('" + String.join("','", depts.split(",")) + "')");
				}
				if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
					dataAuthSql.append(" OR T4.AGENT_ID = '" + user.getUserId() + "'");
				}
				dataAuthSql.append(")");
				if(StringUtils.isNotBlank(obj.getString("all"))) {
					dataAuthSql = new StringBuilder();
				}
			} else {
				dataAuthSql.append(" AND T4.SERIAL_ID='-1' ");
			}
			sql.append(dataAuthSql.toString());
		}

		sql.append(" union all " );
		//全媒体
		sql.append("select T5.SKILL_GROUP_NAME DEPT_NAME,t3.TEMPLATE_ID,t3.RG_CLASS_ID ,t2.SERVICE_TIME,T4.CREATE_CAUSE CREATE_CAUSE1,T4.CREATE_CAUSE CREATE_CAUSE2,t1.ONE_VOTE_ITEM");
		sql.append(",t3.ZN_CLASS_ID,t1.QC_RESULT_ID,t1.DATE_ID,t1.INSPECTOR_ID,t1.INSPECTOR_NAME,t1.QC_TIME,t1.EXAM_GROUP_ID,t1.AGENT_ID,t1.AGENT_NAME,t1.CALL_TYPE,T1.EVALUATE   ");
		sql.append(",t1.TASK_ID,t1.TASK_NAME,t1.PASS_SCORE ,t1.SCORE,CASE WHEN t1.SCORE IS NOT NULL THEN t1.SCORE ELSE t2.ZN_QC_SCORE END AS ACTUAL_SCORE,t1.PASS_FLAG,t1.RESULT_DESC,t1.APPROVAL_STATE,t1.RESULT_CONTENT,t1.BASE_SCORE,t2.SERIAL_ID,t2.ID AS OBJ_ID,t2.ZN_QC_SCORE ZN_SCORE");
		sql.append(",t1.CACH_RESULT,t1.CACH_STATE,t1.CACH_EXJOSN,t1.EVALUATE_EXPLAIN,t2.OBJ_TYPE,T4.CUST_CODE AS CALLER,T4.SERIAL_ID AS CHANNEL_SERIAL_ID,t2.BUSI_TYPE");
		sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT t1"));
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ t2") + " ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK t3") + " ON T3.ID = t2.TASK_ID");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_MEDIA_RECORD") + " T4 ON T4.SERIAL_ID = t2.SERIAL_ID AND t2.CHANNEL_TYPE='2'");
		sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T2.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T2.ENT_ID=T5.ENT_ID");
		sql.append("where 1=1");

		//评定等级
		sql.append(param.getString("evaluate"), " and t1.EVALUATE = ?");
		sql.append(param.getString("evaluate2"), " and t1.EVALUATE2 = ?");
		sql.append(param.getString("evaluate3"), " and t1.EVALUATE3 = ?");

		sql.append("and t2.CHANNEL_TYPE='2'");
		sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
		sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
		//数据必须由坐席结案
		sql.append("and t1.APPROVAL_STATE = '5'");
		//需要辅导的数据
		sql.append("and t1.CACH_STATE != '0' ");
		if(StringUtil.equals("Y", openCoachScore)) {
			sql.append(Constants.getPassCoachScore(user.getSchemaName(),user.getEpCode(),user.getBusiOrderId())," and t1.SCORE <= ?");
		}
		//名单类型 0未分类 1优秀案例 2警示案例
		sql.append(param.getString("objType"), "and t2.OBJ_TYPE = ?");


		sql.append(param.getInteger("passFlag"), " and t1.PASS_FLAG = ?");
		sql.append(param.getInteger("callType"), " and t1.CALL_TYPE = ?");
		sql.append(param.getString("cachState"), "and t1.CACH_STATE = ?");
//        sql.append(param.getString("approvalState"), "and t1.APPROVAL_STATE = ?");
		sql.append(param.getString("cachResult"), "and t1.CACH_RESULT = ?");
		sql.append(param.getString("busiType"),"and t2.BUSI_TYPE = ?");
		if(StringUtils.isNotBlank(startDate)){
			sql.append(CommonUtil.parseInteger(startDate.replaceAll("-", "")), " and t1.DATE_ID >= ?");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(CommonUtil.parseInteger(endDate.replaceAll("-", "")), " and t1.DATE_ID <= ?");
		}
		/*if(beforeNum != 0){
			sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), " and t1.DATE_ID <= ?");
		}*/
		if(StringUtils.isNotBlank(begin_time)){
			sql.append(begin_time+" 00:00:00","and t2.SERVICE_TIME >=?");
		}
		if(StringUtils.isNotBlank(end_time)){
			sql.append(end_time+" 23:59:59", " and t2.SERVICE_TIME <=?");
		}
		sql.append(task," AND t1.TASK_ID = ? ");
		if(StringUtil.equals("1", isExcellent)) {
			sql.append(excellentGradeCode," AND t1.EVALUATE = ? ");
		}

		sql.appendLike(param.getString("agentName"), " and t1.AGENT_NAME like ?");
		sql.append(param.getString("examGroupId"), " and t1.EXAM_GROUP_ID = ?");
		sql.append(param.getString("taskId"), " and t1.TASK_ID = ?");
		if(StringUtils.isNotBlank(oneVoteItem)){
			if("Y".equals(oneVoteItem)){
				sql.append(" and t1.ONE_VOTE_ITEM >0");
			}else if("N".equals(oneVoteItem)){
				sql.append(" and t1.ONE_VOTE_ITEM =0 ");
			}
		}



		//isInspector 质检员看自己数据时才传的参数
		if(CommonUtil.isNotBlank(param.getString("isInspector")) || param.getBooleanValue("isInspector")) {
			//质检员查看数据
			sql.append(userId, " and t1.INSPECTOR_ID = ?");
		}else if(!StringUtil.equals("1", isExcellent)) {// 不是查的优秀录音页面需要数据权限
			//使用数据权限
			//原权限为 ： 如果是坐席，只能看到自己被质检结果；质检员，只能看到自己质检结果；  现在改为使用统一的数据权限
			// 获取数据权限
			JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(user.getEpCode(), user.getBusiOrderId(), user.getUserAcc() + "~" + "cc-qc-zjjg"); // ‘人工质检结果’菜单目录ID
			StringBuilder dataAuthSql = new StringBuilder();
			if(obj != null) {
				String depts = obj.getString("depts");
				dataAuthSql.append(" AND ( T4.SERIAL_ID='-1' ");
				if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
					dataAuthSql.append(" OR T4.AGENT_DEPT IN ('" + String.join("','", depts.split(",")) + "')");
				}
				if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
					dataAuthSql.append(" OR T4.AGENT_ID = '" + user.getUserAcc() + "'");
				}
				dataAuthSql.append(")");
				if(StringUtils.isNotBlank(obj.getString("all"))) {
					dataAuthSql = new StringBuilder();
				}
			} else {
				dataAuthSql.append(" AND T4.SERIAL_ID='-1' ");
			}
			sql.append(dataAuthSql.toString());
		}


		String emailOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OPEN_EMAIL_QC");

		if("Y".equals(emailOpen)) {
			//邮件
			sql.append(" union all " );

			sql.append("select T5.SKILL_GROUP_NAME DEPT_NAME,t3.TEMPLATE_ID,t3.RG_CLASS_ID ,t2.SERVICE_TIME,-1 as  CREATE_CAUSE1,-1 as  CREATE_CAUSE2,t1.ONE_VOTE_ITEM");
			sql.append(",t3.ZN_CLASS_ID,t1.QC_RESULT_ID,t1.DATE_ID,t1.INSPECTOR_ID,t1.INSPECTOR_NAME,t1.QC_TIME,t1.EXAM_GROUP_ID,t1.AGENT_ID,t1.AGENT_NAME,t1.CALL_TYPE,T1.EVALUATE   ");
			sql.append(",t1.TASK_ID,t1.TASK_NAME,t1.PASS_SCORE ,t1.SCORE,CASE WHEN t1.SCORE IS NOT NULL THEN t1.SCORE ELSE t2.ZN_QC_SCORE END AS ACTUAL_SCORE,t1.PASS_FLAG,t1.RESULT_DESC,t1.APPROVAL_STATE,t1.RESULT_CONTENT,t1.BASE_SCORE,t2.SERIAL_ID,t2.ID AS OBJ_ID,t2.ZN_QC_SCORE ZN_SCORE");
			sql.append(",t1.CACH_RESULT,t1.CACH_STATE,t1.CACH_EXJOSN,t1.EVALUATE_EXPLAIN,t2.OBJ_TYPE,T4.EMAIL_FROM AS CALLER,T4.ID AS CHANNEL_SERIAL_ID,t2.BUSI_TYPE");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT t1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ t2") + " ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK t3") + " ON T3.ID = t2.TASK_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_EMAIL_SESSION") + " T4 ON T4.ID = t2.SERIAL_ID AND t2.CHANNEL_TYPE='3'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T2.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T2.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");

			//评定等级
			sql.append(param.getString("evaluate"), " and t1.EVALUATE = ?");
			sql.append(param.getString("evaluate2"), " and t1.EVALUATE2 = ?");
			sql.append(param.getString("evaluate3"), " and t1.EVALUATE3 = ?");

			sql.append("and t2.CHANNEL_TYPE='3'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			//数据必须由坐席结案
			sql.append("and t1.APPROVAL_STATE = '5'");
			//需要辅导的数据
			sql.append("and t1.CACH_STATE != '0' ");
			if(StringUtil.equals("Y", openCoachScore)) {
				sql.append(Constants.getPassCoachScore(user.getSchemaName(),user.getEpCode(),user.getBusiOrderId())," and t1.SCORE <= ?");
			}
			//名单类型 0未分类 1优秀案例 2警示案例
			sql.append(param.getString("objType"), "and t2.OBJ_TYPE = ?");

			sql.append(param.getInteger("passFlag"), " and t1.PASS_FLAG = ?");
			sql.append(param.getInteger("callType"), " and t1.CALL_TYPE = ?");
			sql.append(param.getString("cachState"), "and t1.CACH_STATE = ?");
//            sql.append(param.getString("approvalState"), "and t1.APPROVAL_STATE = ?");
			sql.append(param.getString("cachResult"), "and t1.CACH_RESULT = ?");
			sql.append(param.getString("busiType"),"and t2.BUSI_TYPE = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replaceAll("-", "")), " and t1.DATE_ID >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replaceAll("-", "")), " and t1.DATE_ID <= ?");
			}
			/*if(beforeNum != 0){
				sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), " and t1.DATE_ID <= ?");
			}*/
			if(StringUtils.isNotBlank(begin_time)){
				sql.append(begin_time+" 00:00:00","and t2.SERVICE_TIME >=?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " and t2.SERVICE_TIME <=?");
			}
			sql.append(task," AND t1.TASK_ID = ? ");
			if(StringUtil.equals("1", isExcellent)) {
				sql.append(excellentGradeCode," AND t1.EVALUATE = ? ");
			}
			sql.appendLike(param.getString("agentName"), " and t1.AGENT_NAME like ?");
			sql.append(param.getString("examGroupId"), " and t1.EXAM_GROUP_ID = ?");
			sql.append(param.getString("taskId"), " and t1.TASK_ID = ?");
			if(StringUtils.isNotBlank(oneVoteItem)){
				if("Y".equals(oneVoteItem)){
					sql.append(" and t1.ONE_VOTE_ITEM >0");
				}else if("N".equals(oneVoteItem)){
					sql.append(" and t1.ONE_VOTE_ITEM =0 ");
				}
			}

			//isInspector 质检员看自己数据时才传的参数
			if(CommonUtil.isNotBlank(param.getString("isInspector")) || param.getBooleanValue("isInspector")) {
				//质检员查看数据
				sql.append(userId, " and t1.INSPECTOR_ID = ?");
			}else if(!StringUtil.equals("1", isExcellent)) {// 不是查的优秀录音页面需要数据权限
				//使用数据权限
				//原权限为 ： 如果是坐席，只能看到自己被质检结果；质检员，只能看到自己质检结果；  现在改为使用统一的数据权限
				// 获取数据权限
				JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(user.getEpCode(), user.getBusiOrderId(), user.getUserAcc() + "~" + "cc-qc-zjjg"); // ‘人工质检结果’菜单目录ID
				StringBuilder dataAuthSql = new StringBuilder();
				if(obj != null) {
					String depts = obj.getString("depts");
					dataAuthSql.append(" AND ( T4.ID='-1' ");
					if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
						dataAuthSql.append(" OR T4.FIRST_HANDLE_DEPT_CODE IN ('" + String.join("','", depts.split(",")) + "')");
					}
					if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
						dataAuthSql.append(" OR T4.HANDLE_ACC = '" + user.getUserAcc() + "'");
					}
					dataAuthSql.append(")");
					if(StringUtils.isNotBlank(obj.getString("all"))) {
						dataAuthSql = new StringBuilder();
					}
				} else {
					dataAuthSql.append(" AND T4.ID='-1' ");
				}
				sql.append(dataAuthSql.toString());
			}
		}


		String thirdOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OEPN_THIRD_PARTY_QC");

		if("Y".equals(thirdOpen)) {

			sql.append(" union all " );
			//第三方
			sql.append("select T5.SKILL_GROUP_NAME DEPT_NAME,t3.TEMPLATE_ID,t3.RG_CLASS_ID ,t2.SERVICE_TIME,-1 as CREATE_CAUSE1,-1 as CREATE_CAUSE2,t1.ONE_VOTE_ITEM");//T3.TEMPLATE_ID as CREATE_CAUSE1,'' as CREATE_CAUSE2
			sql.append(",t3.ZN_CLASS_ID,t1.QC_RESULT_ID,t1.DATE_ID,t1.INSPECTOR_ID,t1.INSPECTOR_NAME,t1.QC_TIME,t1.EXAM_GROUP_ID,t1.AGENT_ID,t1.AGENT_NAME,t1.CALL_TYPE,T1.EVALUATE   ");
			sql.append(",t1.TASK_ID,t1.TASK_NAME,t1.PASS_SCORE ,t1.SCORE,CASE WHEN t1.SCORE IS NOT NULL THEN t1.SCORE ELSE t2.ZN_QC_SCORE END AS ACTUAL_SCORE,t1.PASS_FLAG,t1.RESULT_DESC,t1.APPROVAL_STATE,t1.RESULT_CONTENT,t1.BASE_SCORE,t2.SERIAL_ID,t2.ID AS OBJ_ID,t2.ZN_QC_SCORE ZN_SCORE");
			sql.append(",t1.CACH_RESULT,t1.CACH_STATE,t1.CACH_EXJOSN,t1.EVALUATE_EXPLAIN,t2.OBJ_TYPE,T4.CALLER,T4.OBJECT_ID AS CHANNEL_SERIAL_ID,t2.BUSI_TYPE ");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT t1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ t2") + " ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK t3") + " ON T3.ID = t2.TASK_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_THIRD_RECORD") + " T4 ON T4.SERIAL_ID = t2.SERIAL_ID AND t2.CHANNEL_TYPE='9'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T2.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T2.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");

			//评定等级
			sql.append(param.getString("evaluate"), " and t1.EVALUATE = ?");
			sql.append(param.getString("evaluate2"), " and t1.EVALUATE2 = ?");
			sql.append(param.getString("evaluate3"), " and t1.EVALUATE3 = ?");

			sql.append("and t2.CHANNEL_TYPE='9'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			//数据必须由坐席结案
			sql.append("and t1.APPROVAL_STATE = '5'");
			//需要辅导的数据
			sql.append("and t1.CACH_STATE != '0' ");
			if(StringUtil.equals("Y", openCoachScore)) {
				sql.append(Constants.getPassCoachScore(user.getSchemaName(),user.getEpCode(),user.getBusiOrderId())," and t1.SCORE <= ?");
			}
			//名单类型 0未分类 1优秀案例 2警示案例
			sql.append(param.getString("objType"), "and t2.OBJ_TYPE = ?");

			sql.append(param.getInteger("passFlag"), " and t1.PASS_FLAG = ?");
			sql.append(param.getInteger("callType"), " and t1.CALL_TYPE = ?");
			sql.append(param.getString("cachState"), "and t1.CACH_STATE = ?");
//            sql.append(param.getString("approvalState"), "and t1.APPROVAL_STATE = ?");
			sql.append(param.getString("cachResult"), "and t1.CACH_RESULT = ?");
			sql.append(param.getString("busiType"),"and t2.BUSI_TYPE = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replaceAll("-", "")), " and t1.DATE_ID >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replaceAll("-", "")), " and t1.DATE_ID <= ?");
			}
			/*if(beforeNum != 0){
				sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), " and t1.DATE_ID <= ?");
			}*/
			if(StringUtils.isNotBlank(begin_time)){
				sql.append(begin_time+" 00:00:00","and t2.SERVICE_TIME >=?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " and t2.SERVICE_TIME <=?");
			}
			sql.append(task," AND t1.TASK_ID = ? ");
			if(StringUtil.equals("1", isExcellent)) {
				sql.append(excellentGradeCode," AND t1.EVALUATE = ? ");
			}
			sql.appendLike(param.getString("agentName"), " and t1.AGENT_NAME like ?");
			sql.append(param.getString("examGroupId"), " and t1.EXAM_GROUP_ID = ?");
			sql.append(param.getString("taskId"), " and t1.TASK_ID = ?");
			if(StringUtils.isNotBlank(oneVoteItem)){
				if("Y".equals(oneVoteItem)){
					sql.append(" and t1.ONE_VOTE_ITEM >0");
				}else if("N".equals(oneVoteItem)){
					sql.append(" and t1.ONE_VOTE_ITEM =0 ");
				}
			}

			//isInspector 质检员看自己数据时才传的参数
			if(CommonUtil.isNotBlank(param.getString("isInspector")) || param.getBooleanValue("isInspector")) {
				//质检员查看数据
				sql.append(userId, " and t1.INSPECTOR_ID = ?");
			}else if(!StringUtil.equals("1", isExcellent)) {// 不是查的优秀录音页面需要数据权限
				//使用数据权限
				//原权限为 ： 如果是坐席，只能看到自己被质检结果；质检员，只能看到自己质检结果；  现在改为使用统一的数据权限
				// 获取数据权限
	  	 		/*JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(user.getEpCode(), user.getBusiOrderId(), user.getUserAcc() + "~" + "cc-qc-zjjg"); // ‘人工质检结果’菜单目录ID
	  	 		StringBuilder dataAuthSql = new StringBuilder();
	  	 		if(obj != null) {
	  	 			String depts = obj.getString("depts");
	  	 			dataAuthSql.append(" AND ( T2.SERIAL_ID='-1' ");
	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
	  	 				dataAuthSql.append(" OR T3.AGENT_DEPT_CODE IN ('" + String.join("','", depts.split(",")) + "')");
	  	 			}
	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
	  	 				dataAuthSql.append(" OR T3.AGENT_ACC = '" + user.getUserAcc() + "'");
	  	 			}
	  	 			dataAuthSql.append(")");
	  	 			if(StringUtils.isNotBlank(obj.getString("all"))) {
	  	 				dataAuthSql = new StringBuilder();
	  	 			}
	  	 		} else {
	  				dataAuthSql.append(" AND T3.SERIAL_ID='-1' ");
	  			}
	  			sql.append(dataAuthSql.toString())*/;
			}

		}

		String orderOpen = SystemParamUtil.getEntParam(user.getSchemaName(), user.getEpCode(), user.getBusiOrderId(), Constants.APP_NAME, "OEPN_ORDER_QC");
		logger.info("是否开启工单质检:"+orderOpen);
		if("Y".equals(orderOpen)) {

			sql.append(" union all " );
			//工单
			sql.append("select T5.SKILL_GROUP_NAME DEPT_NAME,t3.TEMPLATE_ID,t3.RG_CLASS_ID ,t2.SERVICE_TIME,'' as CREATE_CAUSE1,'' as CREATE_CAUSE2,t1.ONE_VOTE_ITEM");//T3.TEMPLATE_ID as CREATE_CAUSE1,'' as CREATE_CAUSE2
			sql.append(",t3.ZN_CLASS_ID,t1.QC_RESULT_ID,t1.DATE_ID,t1.INSPECTOR_ID,t1.INSPECTOR_NAME,t1.QC_TIME,t1.EXAM_GROUP_ID,t1.AGENT_ID,t1.AGENT_NAME,t1.CALL_TYPE,T1.EVALUATE   ");
			sql.append(",t1.TASK_ID,t1.TASK_NAME,t1.PASS_SCORE ,t1.SCORE,CASE WHEN t1.SCORE IS NOT NULL THEN t1.SCORE ELSE t2.ZN_QC_SCORE END AS ACTUAL_SCORE,t1.PASS_FLAG,t1.RESULT_DESC,t1.APPROVAL_STATE,t1.RESULT_CONTENT,t1.BASE_SCORE,t2.SERIAL_ID,t2.ID AS OBJ_ID,t2.ZN_QC_SCORE ZN_SCORE");
			sql.append(",t1.CACH_RESULT,t1.CACH_STATE,t1.CACH_EXJOSN,t1.EVALUATE_EXPLAIN,t2.OBJ_TYPE,T4.CALLER,T4.ORDER_NO AS CHANNEL_SERIAL_ID,t2.BUSI_TYPE");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT t1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ t2") + " ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK t3") + " ON T3.ID = t2.TASK_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_BO_BASE_ORDER") + " T4 ON T4.ID = t2.SERIAL_ID AND t2.CHANNEL_TYPE='4'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T2.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T2.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");

			//评定等级
			sql.append(param.getString("evaluate"), " and t1.EVALUATE = ?");
			sql.append(param.getString("evaluate2"), " and t1.EVALUATE2 = ?");
			sql.append(param.getString("evaluate3"), " and t1.EVALUATE3 = ?");

			sql.append("and t2.CHANNEL_TYPE='4'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			//数据必须由坐席结案
			sql.append("and t1.APPROVAL_STATE = '5'");
			//需要辅导的数据
			sql.append("and t1.CACH_STATE != '0' ");
			if(StringUtil.equals("Y", openCoachScore)) {
				sql.append(Constants.getPassCoachScore(user.getSchemaName(),user.getEpCode(),user.getBusiOrderId())," and t1.SCORE <= ?");
			}
			//名单类型 0未分类 1优秀案例 2警示案例
			sql.append(param.getString("objType"), "and t2.OBJ_TYPE = ?");

			sql.append(param.getInteger("passFlag"), " and t1.PASS_FLAG = ?");
			sql.append(param.getInteger("callType"), " and t1.CALL_TYPE = ?");
			sql.append(param.getString("cachState"), "and t1.CACH_STATE = ?");
//            sql.append(param.getString("approvalState"), "and t1.APPROVAL_STATE = ?");
			sql.append(param.getString("cachResult"), "and t1.CACH_RESULT = ?");
			sql.append(param.getString("busiType"),"and t2.BUSI_TYPE = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replaceAll("-", "")), " and t1.DATE_ID >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replaceAll("-", "")), " and t1.DATE_ID <= ?");
			}
			//防止出现重复工单数据
			sql.append(user.getEpCode(), "AND T4.EP_CODE=?");
			/*if(beforeNum != 0){
				sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), " and t1.DATE_ID <= ?");
			}*/
			if(StringUtils.isNotBlank(begin_time)){
				sql.append(begin_time+" 00:00:00","and t2.SERVICE_TIME >=?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " and t2.SERVICE_TIME <=?");
			}
			sql.append(task," AND t1.TASK_ID = ? ");
			if(StringUtil.equals("1", isExcellent)) {
				sql.append(excellentGradeCode," AND t1.EVALUATE = ? ");
			}
			sql.appendLike(param.getString("agentName"), " and t1.AGENT_NAME like ?");
			sql.append(param.getString("examGroupId"), " and t1.EXAM_GROUP_ID = ?");
			sql.append(param.getString("taskId"), " and t1.TASK_ID = ?");
			if(StringUtils.isNotBlank(oneVoteItem)){
				if("Y".equals(oneVoteItem)){
					sql.append(" and t1.ONE_VOTE_ITEM >0");
				}else if("N".equals(oneVoteItem)){
					sql.append(" and t1.ONE_VOTE_ITEM =0 ");
				}
			}

			//isInspector 质检员看自己数据时才传的参数
			if(CommonUtil.isNotBlank(param.getString("isInspector")) || param.getBooleanValue("isInspector")) {
				//质检员查看数据
				sql.append(userId, " and t1.INSPECTOR_ID = ?");
			}else if(!StringUtil.equals("1", isExcellent)) {// 不是查的优秀录音页面需要数据权限
				//使用数据权限
				//原权限为 ： 如果是坐席，只能看到自己被质检结果；质检员，只能看到自己质检结果；  现在改为使用统一的数据权限
				// 获取数据权限
	  	 		/*JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(user.getEpCode(), user.getBusiOrderId(), user.getUserAcc() + "~" + "cc-qc-zjjg"); // ‘人工质检结果’菜单目录ID
	  	 		StringBuilder dataAuthSql = new StringBuilder();
	  	 		if(obj != null) {
	  	 			String depts = obj.getString("depts");
	  	 			dataAuthSql.append(" AND ( T3.SERIAL_ID='-1' ");
	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
	  	 				dataAuthSql.append(" OR T3.AGENT_DEPT_CODE IN ('" + String.join("','", depts.split(",")) + "')");
	  	 			}
	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
	  	 				dataAuthSql.append(" OR T3.AGENT_ACC = '" + user.getUserAcc() + "'");
	  	 			}
	  	 			dataAuthSql.append(")");
	  	 			if(StringUtils.isNotBlank(obj.getString("all"))) {
	  	 				dataAuthSql = new StringBuilder();
	  	 			}
	  	 		} else {
	  				dataAuthSql.append(" AND T3.SERIAL_ID='-1' ");
	  			}
	  			sql.append(dataAuthSql.toString());*/
			}
			sql.append(" union all " );
			//工单老化表
			sql.append("select T5.SKILL_GROUP_NAME DEPT_NAME,t3.TEMPLATE_ID,t3.RG_CLASS_ID ,t2.SERVICE_TIME,'' as CREATE_CAUSE1,'' as CREATE_CAUSE2,t1.ONE_VOTE_ITEM");//T3.TEMPLATE_ID as CREATE_CAUSE1,'' as CREATE_CAUSE2
			sql.append(",t3.ZN_CLASS_ID,t1.QC_RESULT_ID,t1.DATE_ID,t1.INSPECTOR_ID,t1.INSPECTOR_NAME,t1.QC_TIME,t1.EXAM_GROUP_ID,t1.AGENT_ID,t1.AGENT_NAME,t1.CALL_TYPE,T1.EVALUATE   ");
			sql.append(",t1.TASK_ID,t1.TASK_NAME,t1.PASS_SCORE ,t1.SCORE,CASE WHEN t1.SCORE IS NOT NULL THEN t1.SCORE ELSE t2.ZN_QC_SCORE END AS ACTUAL_SCORE,t1.PASS_FLAG,t1.RESULT_DESC,t1.APPROVAL_STATE,t1.RESULT_CONTENT,t1.BASE_SCORE,t2.SERIAL_ID,t2.ID AS OBJ_ID,t2.ZN_QC_SCORE ZN_SCORE");
			sql.append(",t1.CACH_RESULT,t1.CACH_STATE,t1.CACH_EXJOSN,t1.EVALUATE_EXPLAIN,t2.OBJ_TYPE,T4.CALLER,T4.ORDER_NO AS CHANNEL_SERIAL_ID,t2.BUSI_TYPE");
			sql.append("from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT t1"));
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ t2") + " ON t1.QC_RESULT_ID = t2.RG_RESULT_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK t3") + " ON T3.ID = t2.TASK_ID");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "C_BO_BASE_ORDER_HIS") + " T4 ON T4.ID = t2.SERIAL_ID AND t2.CHANNEL_TYPE='4'");
			sql.append("LEFT JOIN " + CommonUtil.getTableName(user.getSchemaName(), "CC_SKILL_GROUP") + " T5 ON T5.SKILL_GROUP_TYPE='struct' AND T2.AGENT_DEPT_CODE = T5.SKILL_GROUP_CODE AND T2.ENT_ID=T5.ENT_ID");
			sql.append("where 1=1");

			//评定等级
			sql.append(param.getString("evaluate"), " and t1.EVALUATE = ?");
			sql.append(param.getString("evaluate2"), " and t1.EVALUATE2 = ?");
			sql.append(param.getString("evaluate3"), " and t1.EVALUATE3 = ?");

			sql.append("and t2.CHANNEL_TYPE='4'");
			sql.append(user.getEpCode(), " and t1.ENT_ID = ?");
			sql.append(user.getBusiOrderId(), " and t1.BUSI_ORDER_ID = ?");
			//数据必须由坐席结案
			sql.append("and t1.APPROVAL_STATE = '5'");
			//需要辅导的数据
			sql.append("and t1.CACH_STATE != '0' ");
			if(StringUtil.equals("Y", openCoachScore)) {
				sql.append(Constants.getPassCoachScore(user.getSchemaName(),user.getEpCode(),user.getBusiOrderId())," and t1.SCORE <= ?");
			}

			//名单类型 0未分类 1优秀案例 2警示案例
			sql.append(param.getString("objType"), "and t2.OBJ_TYPE = ?");

			sql.append(param.getInteger("passFlag"), " and t1.PASS_FLAG = ?");
			sql.append(param.getInteger("callType"), " and t1.CALL_TYPE = ?");
			sql.append(param.getString("cachState"), "and t1.CACH_STATE = ?");
//            sql.append(param.getString("approvalState"), "and t1.APPROVAL_STATE = ?");
			sql.append(param.getString("cachResult"), "and t1.CACH_RESULT = ?");
			sql.append(param.getString("busiType"),"and t2.BUSI_TYPE = ?");
			if(StringUtils.isNotBlank(startDate)){
				sql.append(CommonUtil.parseInteger(startDate.replaceAll("-", "")), " and t1.DATE_ID >= ?");
			}
			if(StringUtils.isNotBlank(endDate)){
				sql.append(CommonUtil.parseInteger(endDate.replaceAll("-", "")), " and t1.DATE_ID <= ?");
			}
			//防止出现重复工单数据
			sql.append(user.getEpCode(), "AND T4.EP_CODE=?");
			/*if(beforeNum != 0){
				sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), " and t1.DATE_ID <= ?");
			}*/
			if(StringUtils.isNotBlank(begin_time)){
				sql.append(begin_time+" 00:00:00","and t2.SERVICE_TIME >=?");
			}
			if(StringUtils.isNotBlank(end_time)){
				sql.append(end_time+" 23:59:59", " and t2.SERVICE_TIME <=?");
			}
			sql.append(task," AND t1.TASK_ID = ? ");
			if(StringUtil.equals("1", isExcellent)) {
				sql.append(excellentGradeCode," AND t1.EVALUATE = ? ");
			}
			sql.appendLike(param.getString("agentName"), " and t1.AGENT_NAME like ?");
			sql.append(param.getString("examGroupId"), " and t1.EXAM_GROUP_ID = ?");
			sql.append(param.getString("taskId"), " and t1.TASK_ID = ?");
			if(StringUtils.isNotBlank(oneVoteItem)){
				if("Y".equals(oneVoteItem)){
					sql.append(" and t1.ONE_VOTE_ITEM >0");
				}else if("N".equals(oneVoteItem)){
					sql.append(" and t1.ONE_VOTE_ITEM =0 ");
				}
			}

			//isInspector 质检员看自己数据时才传的参数
			if(CommonUtil.isNotBlank(param.getString("isInspector")) || param.getBooleanValue("isInspector")) {
				//质检员查看数据
				sql.append(userId, " and t1.INSPECTOR_ID = ?");
			}

		}

		sql.append(") TT ");
		sql.append("left join  "+CommonUtil.getTableName(user.getSchemaName(),"cc_qc_admin_select")+" TT2 ON TT2.TASK_OBJ_ID = TT.OBJ_ID");
		String sortName = param.getString("sortName");
		String sortType = param.getString("sortType");
		if(StringUtils.isBlank(sortType) || StringUtils.isBlank(sortName)) {
			sql.append(" ORDER BY  TT.DATE_ID desc,TT.QC_TIME desc ");
		}else {
			sql.appendSort(sortName, sortType, " TT.DATE_ID desc,TT.QC_TIME desc");
		}
		return sql;
	}
	/**
	 * 获取人工质检明细列表sql
	 * @return
	 */
	public static EasySQL getRgResultInfoListSql(UserModel user, JSONObject param){
		String entId = user.getEpCode();
		String resultConfigValue = SystemParamUtil.getEntParam(user.getSchemaName(), entId, user.getBusiOrderId(), Constants.APP_NAME, "RESULT_CONFIG");
		//是否开启发布功能
		String openIssue =  SystemParamUtil.getEntParam(user.getSchemaName(), entId, user.getBusiOrderId(), Constants.APP_NAME, "OPEN_ISSUE");
		String nowTime = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
		int beforeNum = CommonUtil.parseInt(resultConfigValue, 0);
		String beforeTime = "";
		if(beforeNum != 0){
			beforeTime = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, nowTime, beforeNum * -1);
		}
		PhoneCryptor cryptor = PhoneCryptor.getInstance();
		
		String taskId=param.getString("taskId");
		String taskType=param.getString("taskType");
		String startDate = param.getString("beginStartDate");
		String endDate = param.getString("endStartDate");
		EasySQL sql = new EasySQL();
		if(StringUtils.equals("1", taskType)) {
			sql.append("select a.*,b.IS_TYPEICAL,b.SERIAL_ID AS SERIAL_ID_OBJ,c.CALLER, c.CALLED,c.BILL_BEGIN_TIME,c.BILL_END_TIME,c.BILL_TIME,d.AREA_NAME,c.SATISF_ID  from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT")+"  a   ");
			sql.append("LEFT JOIN "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ")+" b on b.RG_RESULT_ID=a.QC_RESULT_ID ");
			sql.append("LEFT JOIN "+CommonUtil.getTableName(user.getSchemaName(), "CC_CALL_RECORD")+" c on c.SERIAL_ID = b.SERIAL_ID ");
			sql.append("LEFT JOIN CC_AREA d on c.AREA_CODE = d.AREA_CODE ");	
		}else if(StringUtils.equals("2", taskType)){
			sql.append("select a.*,b.IS_TYPEICAL,c.SERIAL_ID AS SERIAL_ID_OBJ,c.SESSION_ID,c.BEGIN_TIME AS BILL_BEGIN_TIME,c.END_TIME AS BILL_END_TIME,c.REQ_TIME,c.SERVER_TIME,c.SATISF_CODE,c.SESSION_SEQ from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT")+"  a   ");
			sql.append("LEFT JOIN "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ")+" b on b.RG_RESULT_ID=a.QC_RESULT_ID ");
			sql.append("LEFT JOIN "+CommonUtil.getTableName(user.getSchemaName(), "CC_MEDIA_RECORD")+" c on c.SERIAL_ID = b.SERIAL_ID ");
		}else if(StringUtils.equals("3", taskType)){
			sql.append("select a.*,b.IS_TYPEICAL,c.ID AS SERIAL_ID_OBJ,c.SESSION_ID,c.CREATE_TIME AS BILL_BEGIN_TIME,c.CLOSE_TIME AS BILL_END_TIME  from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT")+"  a   ");
			sql.append("LEFT JOIN "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ")+" b on b.RG_RESULT_ID=a.QC_RESULT_ID ");
			sql.append("LEFT JOIN "+CommonUtil.getTableName(user.getSchemaName(), "c_email_session")+" c on c.ID = b.SERIAL_ID ");
		}else if(StringUtils.equals("9", taskType)){
			sql.append("select a.*,b.IS_TYPEICAL,c.ID AS SERIAL_ID_OBJ,c.SERIAL_ID SESSION_ID,c.CREATE_TIME AS BILL_BEGIN_TIME,'' AS BILL_END_TIME  from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT")+"  a   ");
			sql.append("LEFT JOIN "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ")+" b on b.RG_RESULT_ID=a.QC_RESULT_ID ");
			sql.append("LEFT JOIN "+CommonUtil.getTableName(user.getSchemaName(), "cc_qc_third_record")+" c on c.SERIAL_ID = b.SERIAL_ID ");
		}else if(StringUtils.equals("4", taskType)){
			sql.append("select a.*,b.IS_TYPEICAL,c.ID AS SERIAL_ID_OBJ,c.ID SESSION_ID,c.CREATE_TIME AS BILL_BEGIN_TIME,C.END_TIME AS BILL_END_TIME  from "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_RESULT")+"  a   ");
			sql.append("LEFT JOIN "+CommonUtil.getTableName(user.getSchemaName(), "CC_QC_TASK_OBJ")+" b on b.RG_RESULT_ID=a.QC_RESULT_ID ");
			sql.append("LEFT JOIN "+CommonUtil.getTableName(user.getSchemaName(), "C_BO_BASE_ORDER")+" c on c.ID = b.SERIAL_ID ");
		}
		sql.append("where 1=1 ");
		sql.append(taskId," AND a.TASK_ID = ? ",false);
		sql.append(taskId," AND b.TASK_ID = ? ",false);
		sql.append(param.getString("groupId")," AND a.EXAM_GROUP_ID = ? ");
		sql.append(param.getInteger("passFlag")," AND a.PASS_FLAG = ? ");
		sql.append(param.getInteger("callType")," AND a.CALL_TYPE = ? ");
		sql.append(param.getInteger("state")," AND a.APPROVAL_STATE = ? ");
		if(StringUtil.equals("Y", openIssue)) {
			//开启发布功能只查状态为4的数据
			sql.append(" and b.RG_STATE = '4'");
		}else {
			sql.append(" and b.RG_STATE in ('3', '4') ");
		}
		if(StringUtils.equals("2", taskType)){
			sql.appendLike(param.getString("serial")," AND c.SERIAL_ID LIKE ? ");
		}else if(StringUtils.equals("1", taskType)){
			sql.appendLike(param.getString("serial")," AND b.SERIAL_ID LIKE ? ");
		}else if(StringUtils.equals("3", taskType)){
			sql.appendLike(param.getString("serial")," AND c.ID LIKE ? ");
		}
		String isTypeical = param.getString("isTypeical");
		if(StringUtils.equals("02", isTypeical)) {
			sql.append(" AND b.IS_TYPEICAL='02' ");
		}else if(StringUtils.equals("01", isTypeical)) {
			sql.append(" AND (b.IS_TYPEICAL='01' OR b.IS_TYPEICAL='' OR b.IS_TYPEICAL is null) ");
		}
		if(StringUtils.equals("1", taskType)) {
			sql.append(param.getInteger("voiceSatif"), "AND c.SATISF_ID=?");
			sql.append(param.getString("beginVoiceDate"), "AND c.BILL_BEGIN_TIME>=?");
			sql.append(param.getString("endVoiceDate"), "AND c.BILL_BEGIN_TIME<=?");
			
			String caller = param.getString("caller");
			String called = param.getString("called");
			
			if(StringUtils.isNotBlank(caller)){
				sql.append("and c.CALLER in('"+cryptor.encrypt(entId, caller, cryptor.BUSI_TYPE_PHONE)+"','"+caller+"') ");
			}
			if(StringUtils.isNotBlank(called)){
				sql.append("and c.CALLED in('"+cryptor.encrypt(entId, called, cryptor.BUSI_TYPE_PHONE)+"','"+called+"') ");
			}
			
		}else if(StringUtils.equals("2", taskType)){
			sql.append(param.getString("mediaSatif"), "AND c.SATISF_CODE=?");
			sql.append(param.getInteger("sessionSeq"), "AND c.SESSION_SEQ=?");
			sql.append(param.getString("beginMediaDate"), "AND c.BEGIN_TIME>=?");
			sql.append(param.getString("endMediaDate"), "AND c.BEGIN_TIME<=?");
			sql.appendLike(param.getString("sessionId"), "AND c.SESSION_ID like ?");
		}
		String oneVoteItem = param.getString("oneVoteItem");
        if(StringUtils.isNotBlank(oneVoteItem)){
        	if("Y".equals(oneVoteItem)){
        		sql.append(" and a.ONE_VOTE_ITEM >0"); 
        	}else if("N".equals(oneVoteItem)){
        		sql.append(" and a.ONE_VOTE_ITEM =0 "); 
        	}
        }
		sql.appendRLike(param.getString("inspectorName")," AND a.INSPECTOR_NAME like ? ");
		sql.appendRLike(param.getString("agentName")," AND a.AGENT_NAME like ? ");
		
		/*
		sql.append(startDate," and a.QC_TIME >= ? ");
		sql.append(endDate, "and a.QC_TIME <= ?");
		*/
		if(StringUtils.isNotBlank(startDate)){
			//DATE_ID为int类型 postgresql需要类型转换
			sql.append(CommonUtil.parseInteger(startDate.replaceAll("-", "")), " and a.DATE_ID >= ?");
		}
		if(StringUtils.isNotBlank(endDate)){
			sql.append(CommonUtil.parseInteger(endDate.replaceAll("-", "")), " and a.DATE_ID <= ?");
		}
		if(beforeNum != 0){
			sql.append(CommonUtil.parseInteger(beforeTime.replaceAll("-", "")), "AND a.DATE_ID<=?");
		}
		 //isInspector 质检员看自己数据时才传的参数
  		if(!"".equals(param.getString("isInspector"))|| param.getBooleanValue("isInspector")) {
  			//质检员查看数据
  			sql.append(user.getUserId(), " and a.INSPECTOR_ID = ?");
  		} else {
  		//使用数据权限
  	 		JSONObject obj = CacheUtil.getUserDataAuthCache().getCache(user.getEpCode(), user.getBusiOrderId(), user.getUserAcc() + "~" + "cc-qc-zjjg"); // ‘人工质检结果’菜单目录ID
  	 		StringBuilder dataAuthSql = new StringBuilder();
  	 		if(obj != null) {
  	 			String agentId = user.getUserId();
  	 			if(StringUtils.equals("2", taskType) || StringUtils.equals("1", taskType)) {
  	 				//语音AGENT_ID存ID,全媒体AGENT_ID存账号
  	 				agentId = user.getUserAcc();
  	 				String depts = obj.getString("depts");
  	  	 			dataAuthSql.append(" AND ( c.SERIAL_ID='-1' ");
  	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
  	  	 				dataAuthSql.append(" OR c.AGENT_DEPT IN ('" + String.join("','", depts.split(",")) + "')");
  	  	 			}
  	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
  	  	 				dataAuthSql.append(" OR c.AGENT_ID = '" + agentId + "'");
  	  	 			}
  	  	 			dataAuthSql.append(")");
  	  	 			if(StringUtils.isNotBlank(obj.getString("all"))) {
  	  	 				dataAuthSql = new StringBuilder();
  	  	 			}
  	 			}else if(StringUtils.equals("3", taskType) ){
  	 				agentId = user.getUserAcc();
  	 				String depts = obj.getString("depts");
  	  	 			dataAuthSql.append(" AND ( b.ID='-1' ");
  	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
  	  	 				dataAuthSql.append(" OR b.FIRST_HANDLE_DEPT_CODE IN ('" + String.join("','", depts.split(",")) + "')");
  	  	 			}
  	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
  	  	 				dataAuthSql.append(" OR b.HANDLE_ACC = '" + agentId + "'");
  	  	 			}
  	  	 			dataAuthSql.append(")");
  	  	 			if(StringUtils.isNotBlank(obj.getString("all"))) {
  	  	 				dataAuthSql = new StringBuilder();
  	  	 			}
  	 			}else if(StringUtils.equals("9", taskType) ){
  	 				agentId = user.getUserAcc();
  	 				String depts = obj.getString("depts");
  	  	 			dataAuthSql.append(" AND ( c.ID='-1' ");
  	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(depts)) {
  	  	 				dataAuthSql.append(" OR c.AGENT_DEPT_CODE IN ('" + String.join("','", depts.split(",")) + "')");
  	  	 			}
  	  	 			if(StringUtils.isBlank(obj.getString("all")) && StringUtils.isNotBlank(obj.getString("oneself"))) {
  	  	 				dataAuthSql.append(" OR c.AGENT_ACC = '" + agentId + "'");
  	  	 			}
  	  	 			dataAuthSql.append(")");
  	  	 			if(StringUtils.isNotBlank(obj.getString("all"))) {
  	  	 				dataAuthSql = new StringBuilder();
  	  	 			}
  	 			}
  	 			
  	 		} else {
  	 			if(StringUtils.equals("2", taskType) || StringUtils.equals("1", taskType)){
  	 				dataAuthSql.append(" AND c.SERIAL_ID='-1' ");
  	 			}else if(StringUtils.equals("3", taskType)){
  	 				dataAuthSql.append(" AND c.ID='-1' ");
  	 			}else if(StringUtils.equals("9", taskType)){
  	 				dataAuthSql.append(" AND c.ID='-1' ");
  	 			}
  				
  			}
  	 		sql.append(dataAuthSql.toString());
  		}
		//工单类型兼容历史表
  		if (StringUtils.equals("4", taskType)) {
  			
  			sql.append("AND C.ID IS NOT NULL ");
  			
  			String hisSql = sql.getSQL().replaceAll("C_BO_BASE_ORDER", "C_BO_BASE_ORDER_HIS");
  			sql.append("UNION ALL");
  			sql.append(hisSql);
  			Object[] params = sql.getParams();
  			if (params!=null) {
  				for (Object obj: params) {
  					sql.addParams(obj);
  				}
  			}
  		}
  		
		String sortName = param.getString("sortName");
		String sortType = param.getString("sortType");
		if(StringUtils.isBlank(sortType) || StringUtils.isBlank(sortName)) {
			sql.append("order by  QC_TIME desc");
		}else {
			sql.appendSort(sortName, sortType, "  QC_TIME desc");
		}
		
		return sql;
	}

	private static String getTableName(String tableNmae, String dbName) {
		if(StringUtils.notBlank(dbName)){
			return dbName+"."+tableNmae;
		}
		return tableNmae;
	}

	public static EasySQL getZnResultInfoListSql(UserModel user, JSONObject param, EasyQuery query) {
		
		return getZnResultListSql(user, param, query);
	}

	/**
	 * 评分项统计SQL
	 * @param user
	 * @param param
	 * @return
	 */
	public static EasySQL getResultStatByTaskAndUser(UserModel user,JSONObject param, EasyQuery query){
		EasySQL sql = new EasySQL();
		String ifnull = "ifnull";
		
		DBTypes dbTypes = query.getTypes();
		if (DBTypes.ORACLE == dbTypes || DBTypes.DAMENG == dbTypes) {
			ifnull = "nvl";
		}
		if(DBTypes.PostgreSql == dbTypes) {
			ifnull = "COALESCE";
		}
		sql.append(" select mainTable.* from (");
			sql.append(" select T3.TASK_ID,T3.AGENT_ID,T3.CALL_TYPE,T2.ITEM_ID,T2.ITEM_NAME,T2.INDEX_NUM");
			sql.append(" ," + ifnull + "(ROUND(SUM(case when T1.VALID_STATE = '1' THEN T1.SCORE ELSE 0 END),2),0) SUM_SCORE");
			sql.append(" ,count(case when T1.VALID_STATE = '1' THEN T1.P_ITEM_ID ELSE NULL END) COUNT ");
		sql.append(" ,(case when count(case when T1.VALID_STATE = '1' THEN T1.P_ITEM_ID ELSE NULL END) > 0 then ROUND(SUM(case when T1.VALID_STATE = '1' THEN T1.SCORE ELSE 0 END) / count(case when T1.VALID_STATE = '1' THEN T1.P_ITEM_ID ELSE NULL END),2) else 0 end) AVG_SCORE");
			sql.append(" from " + CommonUtil.getTableName(user.getSchemaName(),"cc_qc_result_item T1"));
			sql.append(" left join "+ CommonUtil.getTableName(user.getSchemaName(),"cc_qc_result_item T2") + " on T2.RESULT_ITEM_ID = T1.P_ITEM_ID ");
			sql.append(" left join "+ CommonUtil.getTableName(user.getSchemaName(),"cc_qc_result T3") + " on T3.QC_RESULT_ID = T1.QC_RESULT_ID ");
			sql.append(" left join "+ CommonUtil.getTableName(user.getSchemaName(),"cc_qc_task_obj T4") + " on T3.QC_RESULT_ID = T4.RG_RESULT_ID ");
			sql.append(" where T1.P_ITEM_ID != '2000' ");
			sql.append(param.getString("taskId"), " and T3.TASK_ID = ? ",false);
			sql.append("4", " and T4.RG_STATE= ? ");//只统计质检完成的
			sql.append(param.getString("beginStartDate") + " 00:00:00", " and T4.SERVICE_TIME >= ?");
			sql.append(param.getString("endStartDate") + " 23:59:59", " and T4.SERVICE_TIME <= ?");
			sql.append(" GROUP BY T3.TASK_ID,T3.AGENT_ID,T3.CALL_TYPE,T2.ITEM_ID,T2.ITEM_NAME,T2.INDEX_NUM "); //统计评分项信息
		sql.append(" ) mainTable ");
		sql.append(" left join cc_user u on mainTable.AGENT_ID = u.USER_ID");
		sql.append(" where 1 = 1 ");
		sql.append(param.getString("jobNumber")," and u.AGENT_PHONE = ? ");
		sql.append(param.getString("agentAcc")," and u.USER_ACCT = ? ");
		sql.appendLike(param.getString("agentName")," and u.USERNAME like ? ");
		return sql;
	}

	/**
	 * 评分细项统计SQL
	 * @param user
	 * @param param
	 * @return
	 */
	public static EasySQL getResultItemStatByTaskAndUser(UserModel user,JSONObject param, EasyQuery query){
		EasySQL sql = new EasySQL();
		String ifnull = "ifnull";
		
		DBTypes types = query.getTypes();
		
		if(DBTypes.ORACLE == types||DBTypes.DAMENG == types) {
			ifnull = "nvl";
		}
		if(DBTypes.PostgreSql == types) {
			ifnull = "COALESCE";
		}
		sql.append(" select mainTable.* from (");
		sql.append(" select T3.TASK_ID,T3.AGENT_ID,T3.CALL_TYPE,T1.ITEM_ID,T1.INDEX_NUM");
		sql.append(" ," + ifnull + "(ROUND(SUM(case when T1.VALID_STATE = '1' THEN T1.SCORE ELSE 0 END),2),0) SUM_SCORE");
		sql.append(" ,count(case when T1.VALID_STATE = '1' THEN T1.P_ITEM_ID ELSE NULL END) COUNT ");
		sql.append(" ,(case when count(case when T1.VALID_STATE = '1' THEN T1.P_ITEM_ID ELSE NULL END) > 0 then ROUND(SUM(case when T1.VALID_STATE = '1' THEN T1.SCORE ELSE 0 END) / count(case when T1.VALID_STATE = '1' THEN T1.P_ITEM_ID ELSE NULL END),2) else 0 end) AVG_SCORE");
		sql.append(" from " + CommonUtil.getTableName(user.getSchemaName(),"cc_qc_result_item T1"));
		sql.append(" left join "+ CommonUtil.getTableName(user.getSchemaName(),"cc_qc_result_item T2") + " on T2.RESULT_ITEM_ID = T1.P_ITEM_ID ");
		sql.append(" left join "+ CommonUtil.getTableName(user.getSchemaName(),"cc_qc_result T3") + " on T3.QC_RESULT_ID = T1.QC_RESULT_ID ");
		sql.append(" left join "+ CommonUtil.getTableName(user.getSchemaName(),"cc_qc_task_obj T4") + " on T3.QC_RESULT_ID = T4.RG_RESULT_ID ");
		sql.append(" where T1.P_ITEM_ID != '2000' ");
		sql.append(param.getString("taskId")," and T3.TASK_ID = ? ",false);
		sql.append("4", " and T4.RG_STATE= ? ");//只统计质检完成的
		sql.append(param.getString("beginStartDate") + " 00:00:00", " and T4.SERVICE_TIME >= ?");
		sql.append(param.getString("endStartDate") + " 23:59:59", " and T4.SERVICE_TIME <= ?");
		sql.append(" GROUP BY T3.TASK_ID,T3.AGENT_ID,T3.CALL_TYPE,T1.ITEM_ID,T1.INDEX_NUM"); //统计评分细项信息
		sql.append(" ORDER BY T3.AGENT_ID,T3.CALL_TYPE,T1.ITEM_ID,T1.INDEX_NUM");
		sql.append(" ) mainTable ");
		sql.append(" left join cc_user u on mainTable.AGENT_ID = u.USER_ID");
		sql.append(" where 1 = 1 ");
		sql.append(param.getString("jobNumber")," and u.AGENT_PHONE = ? ");
		sql.append(param.getString("agentAcc")," and u.USER_ACCT = ? ");
		sql.appendLike(param.getString("agentName")," and u.USERNAME like ? ");
		return sql;
	}

	/**
	 * 将SQL2合并到SQL1
	 * @param sql1
	 * @param sql2
	 */
	public static void addExecuteSql(EasySQL sql1, EasySQL sql2) {
		logger.info("sql1:"+sql1.getSQL());
		logger.info("sql2:"+sql2.getSQL());

		sql1.append(sql2.getSQL());
		List<Object> params = sql2.getParamsList();
		for (Object param : params) {
			sql1.addParams(param);
		}
	}
}
